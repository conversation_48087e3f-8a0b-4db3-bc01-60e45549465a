/**
 * Tests for the ConsolidatedResourceProvider
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Readable } from 'stream';
import { ConsolidatedResourceProvider } from '../services/analysis/stream/consolidatedResourceProvider.js';
import { PackageIndexEntry } from '../services/analysis/package/streamingPackageReader.js';
import EnhancedMemoryManager from '../utils/memory/enhancedMemoryManager.js';
import { EnhancedBufferPool } from '../utils/memory/enhancedBufferPool.js';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies
vi.mock('../utils/logging/logger.js');
vi.mock('../utils/memory/enhancedMemoryManager.js');
vi.mock('../utils/memory/enhancedBufferPool.js');
vi.mock('fs/promises');

// Mock EnhancedMemoryManager
const mockMemoryManager = {
    getMemoryPressure: vi.fn().mockReturnValue(0.5),
    getMemoryStats: vi.fn().mockReturnValue({
        heapUsed: 100000000,
        heapTotal: 200000000,
        external: 10000000,
        arrayBuffers: 5000000,
        rss: 300000000
    })
};

// Mock EnhancedBufferPool
const mockBufferPool = {
    getBuffer: vi.fn().mockImplementation((size) => Buffer.alloc(size)),
    returnBuffer: vi.fn(),
    cleanup: vi.fn()
};

// Mock file handle
const mockFileHandle = {
    read: vi.fn().mockImplementation((buffer, offset, length, position) => {
        // Fill buffer with test data
        const testData = Buffer.from('Test data');
        testData.copy(buffer, offset, 0, Math.min(length, testData.length));
        return { bytesRead: Math.min(length, testData.length), buffer };
    }),
    stat: vi.fn().mockResolvedValue({ size: 1000000 }),
    close: vi.fn().mockResolvedValue(undefined)
};

// Mock fs.open
vi.mocked(fs.open).mockResolvedValue(mockFileHandle as any);

describe('ConsolidatedResourceProvider', () => {
    let provider: ConsolidatedResourceProvider;
    let mockEntry: PackageIndexEntry;
    
    beforeEach(() => {
        // Create a new provider for each test
        provider = new ConsolidatedResourceProvider();
        
        // Create a mock package index entry
        mockEntry = {
            type: 0x12345678,
            group: 0,
            instanceHi: 0,
            instanceLo: 0,
            offset: 0,
            fileSize: 100,
            memSize: 200,
            compressed: false
        };
        
        // Reset mocks
        vi.clearAllMocks();
        
        // Mock EnhancedMemoryManager.getInstance
        vi.mocked(EnhancedMemoryManager.getInstance).mockReturnValue(mockMemoryManager as any);
        
        // Mock EnhancedBufferPool.getInstance
        vi.mocked(EnhancedBufferPool.getInstance).mockReturnValue(mockBufferPool as any);
    });
    
    afterEach(async () => {
        // Clean up
        await provider.close();
    });
    
    it('should create a resource provider successfully', () => {
        expect(provider).toBeDefined();
    });
    
    it('should create a resource stream for an uncompressed resource', async () => {
        // Create a stream for the resource
        const stream = await provider.createResourceStream('test.package', mockEntry);
        
        // Check that the stream was created
        expect(stream).toBeDefined();
        expect(stream).toBeInstanceOf(Readable);
        
        // Check that fs.open was called
        expect(fs.open).toHaveBeenCalledWith('test.package', 'r');
        
        // Collect data from the stream
        const chunks: Buffer[] = [];
        for await (const chunk of stream) {
            chunks.push(chunk);
        }
        
        // Check that data was read
        expect(chunks.length).toBeGreaterThan(0);
    });
    
    it('should get resource metadata', async () => {
        // Get metadata for the resource
        const metadata = await provider.getResourceMetadata('test.package', mockEntry);
        
        // Check that metadata was returned
        expect(metadata).toBeDefined();
        expect(metadata.size).toBe(mockEntry.fileSize); // Uncompressed size
        expect(metadata.compressed).toBe(mockEntry.compressed);
        expect(metadata.exists).toBe(true);
        
        // Check that fs.open was called
        expect(fs.open).toHaveBeenCalledWith('test.package', 'r');
    });
    
    it('should check if a resource exists', async () => {
        // Check if the resource exists
        const exists = await provider.resourceExists('test.package', mockEntry);
        
        // Check that the result was returned
        expect(exists).toBe(true);
        
        // Check that fs.open was called
        expect(fs.open).toHaveBeenCalledWith('test.package', 'r');
    });
    
    it('should close file handles', async () => {
        // Create a stream to open a file handle
        await provider.createResourceStream('test.package', mockEntry);
        
        // Close the provider
        await provider.close();
        
        // Check that the file handle was closed
        expect(mockFileHandle.close).toHaveBeenCalled();
    });
    
    it('should handle compressed resources', async () => {
        // Create a compressed entry
        const compressedEntry: PackageIndexEntry = {
            ...mockEntry,
            compressed: true,
            fileSize: 50, // Compressed size
            memSize: 100 // Uncompressed size
        };
        
        // Mock zlib.inflate
        const mockInflate = vi.fn().mockResolvedValue(Buffer.from('Decompressed data'));
        vi.mock('zlib', () => ({
            inflate: mockInflate
        }));
        
        // Create a stream for the compressed resource
        const stream = await provider.createResourceStream('test.package', compressedEntry);
        
        // Check that the stream was created
        expect(stream).toBeDefined();
        expect(stream).toBeInstanceOf(Readable);
        
        // Check that fs.open was called
        expect(fs.open).toHaveBeenCalledWith('test.package', 'r');
        
        // Check that buffer was allocated
        expect(mockBufferPool.getBuffer).toHaveBeenCalledWith(compressedEntry.fileSize);
        
        // Check that buffer was returned
        expect(mockBufferPool.returnBuffer).toHaveBeenCalled();
    });
    
    it('should adjust thresholds based on memory pressure', async () => {
        // Mock high memory pressure
        vi.mocked(mockMemoryManager.getMemoryPressure).mockReturnValue(0.9);
        
        // Create a stream to trigger threshold adjustment
        await provider.createResourceStream('test.package', mockEntry);
        
        // Check that memory pressure was checked
        expect(mockMemoryManager.getMemoryPressure).toHaveBeenCalled();
        
        // Reset memory pressure
        vi.mocked(mockMemoryManager.getMemoryPressure).mockReturnValue(0.5);
    });
});
