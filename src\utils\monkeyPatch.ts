/**
 * Monkey Patch Utilities
 *
 * This module provides utilities for monkey patching Node.js classes
 * to fix issues like EventEmitter memory leaks.
 */

import { Logger } from './logging/logger.js';
import { EventEmitter } from 'events';
import * as fs from 'fs';

const logger = new Logger('MonkeyPatch');

/**
 * Apply monkey patches to fix known issues
 */
export function applyMonkeyPatches() {
    try {
        logger.info('Applying monkey patches...');

        // Patch EventEmitter to increase max listeners
        patchEventEmitter();

        // Patch fs.ReadStream and fs.WriteStream to increase max listeners
        patchFileStreams();

        logger.info('Monkey patches applied successfully');
        return true;
    } catch (error) {
        logger.error(`Error applying monkey patches: ${error}`);
        return false;
    }
}

/**
 * Patch EventEmitter to increase max listeners
 */
function patchEventEmitter() {
    try {
        // Store the original setMaxListeners method
        const originalSetMaxListeners = EventEmitter.prototype.setMaxListeners;

        // Override the setMaxListeners method to log calls and handle errors
        EventEmitter.prototype.setMaxListeners = function(n: number) {
            try {
                logger.debug(`Setting max listeners to ${n} for ${this.constructor.name}`);
                return originalSetMaxListeners.call(this, n);
            } catch (error) {
                logger.error(`Error setting max listeners for ${this.constructor.name}: ${error}`);
                // Return this to maintain method chaining
                return this;
            }
        };

        // Set default max listeners to a higher value
        EventEmitter.defaultMaxListeners = 100;
        logger.info('Patched EventEmitter.prototype.setMaxListeners');

        // Patch the addListener method to automatically increase max listeners if needed
        const originalAddListener = EventEmitter.prototype.addListener;

        EventEmitter.prototype.addListener = function(event: string, listener: (...args: any[]) => void) {
            // Get the current number of listeners for this event
            const currentListeners = this.listeners(event).length;

            // If we're about to exceed the max listeners, increase it
            if (currentListeners >= this.getMaxListeners()) {
                const newMax = currentListeners + 10;
                this.setMaxListeners(newMax);
                logger.debug(`Automatically increased max listeners to ${newMax} for ${this.constructor.name}`);
            }

            return originalAddListener.call(this, event, listener);
        };

        // Alias on to addListener
        EventEmitter.prototype.on = EventEmitter.prototype.addListener;

        logger.info('Patched EventEmitter.prototype.addListener');
    } catch (error) {
        logger.error(`Error patching EventEmitter: ${error}`);
    }
}

/**
 * Patch fs.ReadStream and fs.WriteStream to increase max listeners
 */
function patchFileStreams() {
    try {
        // Patch fs.ReadStream
        if (fs.ReadStream && fs.ReadStream.prototype) {
            // Set max listeners for all ReadStream instances
            const originalReadStreamConstructor = fs.ReadStream;

            // @ts-ignore - We're monkey patching here
            fs.ReadStream = function(...args: any[]) {
                const stream = new originalReadStreamConstructor(...args);
                stream.setMaxListeners(100);
                return stream;
            };

            // Copy prototype
            fs.ReadStream.prototype = originalReadStreamConstructor.prototype;

            logger.info('Patched fs.ReadStream constructor');
        }

        // Patch fs.WriteStream
        if (fs.WriteStream && fs.WriteStream.prototype) {
            // Set max listeners for all WriteStream instances
            const originalWriteStreamConstructor = fs.WriteStream;

            // @ts-ignore - We're monkey patching here
            fs.WriteStream = function(...args: any[]) {
                const stream = new originalWriteStreamConstructor(...args);
                stream.setMaxListeners(100);
                return stream;
            };

            // Copy prototype
            fs.WriteStream.prototype = originalWriteStreamConstructor.prototype;

            logger.info('Patched fs.WriteStream constructor');
        }
    } catch (error) {
        logger.error(`Error patching file streams: ${error}`);
    }
}
