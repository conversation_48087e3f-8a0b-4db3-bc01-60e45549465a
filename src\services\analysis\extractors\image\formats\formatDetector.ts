/**
 * Image format detection utilities
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageFormat } from '../types.js';
import { BufferReader } from '../utils/bufferReader.js';

// Create a logger instance
const log = new Logger('ImageFormatDetector');

/**
 * Detects the format of an image buffer
 * @param buffer Image buffer
 * @returns Detected image format
 */
export function detectImageFormat(buffer: Buffer): ImageFormat {
    if (buffer.length < 8) {
        log.warn(`Buffer too small to detect format: ${buffer.length} bytes`);
        return ImageFormat.UNKNOWN;
    }
    
    try {
        // Check for DDS format (signature: "DDS ")
        if (buffer.length >= 4 && buffer.readUInt32LE(0) === 0x20534444) {
            log.debug('Detected DDS format');
            return ImageFormat.DDS;
        }
        
        // Check for PNG format (signature: 89 50 4E 47 0D 0A 1A 0A)
        if (buffer.length >= 8 &&
            buffer[0] === 0x89 &&
            buffer[1] === 0x50 &&
            buffer[2] === 0x4E &&
            buffer[3] === 0x47 &&
            buffer[4] === 0x0D &&
            buffer[5] === 0x0A &&
            buffer[6] === 0x1A &&
            buffer[7] === 0x0A) {
            log.debug('Detected PNG format');
            return ImageFormat.PNG;
        }
        
        // Check for JPEG format (signature: FF D8 FF)
        if (buffer.length >= 3 &&
            buffer[0] === 0xFF &&
            buffer[1] === 0xD8 &&
            buffer[2] === 0xFF) {
            log.debug('Detected JPEG format');
            return ImageFormat.JPEG;
        }
        
        // Check for RLE2 format (Sims 4 specific, signature: "RLE2")
        if (buffer.length >= 4 &&
            buffer[0] === 0x52 && // 'R'
            buffer[1] === 0x4C && // 'L'
            buffer[2] === 0x45 && // 'E'
            buffer[3] === 0x32) { // '2'
            log.debug('Detected RLE2 format (Sims 4 specific)');
            return ImageFormat.RLE2;
        }
        
        // Unknown format
        log.warn(`Unknown image format detected`);
        return ImageFormat.UNKNOWN;
    } catch (error: any) {
        log.error(`Error detecting image format: ${error.message || error}`);
        return ImageFormat.UNKNOWN;
    }
}

/**
 * Checks if a buffer is a valid image
 * @param buffer Buffer to check
 * @returns Whether the buffer is a valid image
 */
export function isValidImage(buffer: Buffer): boolean {
    return detectImageFormat(buffer) !== ImageFormat.UNKNOWN;
}

/**
 * Gets the MIME type for an image format
 * @param format Image format
 * @returns MIME type
 */
export function getMimeType(format: ImageFormat): string {
    switch (format) {
        case ImageFormat.DDS:
            return 'image/vnd.ms-dds';
        case ImageFormat.PNG:
            return 'image/png';
        case ImageFormat.JPEG:
            return 'image/jpeg';
        case ImageFormat.RLE2:
            return 'application/x-sims4-rle2';
        default:
            return 'application/octet-stream';
    }
}
