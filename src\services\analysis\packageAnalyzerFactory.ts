/**
 * Package Analyzer Factory
 *
 * This module provides a factory function for creating package analyzers.
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { SemanticAnalysisService } from './semanticAnalysisService.js';
import { ResourceProcessor } from './package/resourceProcessor.js';
import { PackageLoader } from './package/packageLoader.js';
import { ResourceMetadataExtractor } from './package/resourceMetadataExtractor.js';
import { PackageAnalyzer } from './packageAnalyzer.js';
import { DependencyChainAnalyzer } from './semantic/dependencyChainAnalyzer.js';
import { GameplaySystemAnalyzer } from './gameplay/GameplaySystemAnalyzer.js';
import { ConsolidatedResourceProvider } from './stream/consolidatedResourceProvider.js';
import { ConsolidatedStreamPipeline } from './stream/consolidatedStreamPipeline.js';
import { EnhancedDependencyPipelineAdapter } from './stream/enhancedDependencyPipelineAdapter.js';

// Create a logger for this module
const logger = new Logger('PackageAnalyzerFactory');

/**
 * Package analyzer factory options
 */
export interface PackageAnalyzerFactoryOptions {
    databaseService?: DatabaseService;
    semanticAnalysisService?: SemanticAnalysisService;
    resourceProcessor?: ResourceProcessor;
    packageLoader?: PackageLoader;
    resourceMetadataExtractor?: ResourceMetadataExtractor;
    dependencyChainAnalyzer?: DependencyChainAnalyzer;
    gameplaySystemAnalyzer?: GameplaySystemAnalyzer;
    resourceProvider?: ConsolidatedResourceProvider;
    streamPipeline?: ConsolidatedStreamPipeline;
    dependencyPipelineAdapter?: EnhancedDependencyPipelineAdapter;
}

/**
 * Create a package analyzer
 * @param options Factory options
 * @returns Package analyzer instance
 */
export function createPackageAnalyzer(options: PackageAnalyzerFactoryOptions = {}): PackageAnalyzer {
    const {
        databaseService = new DatabaseService(),
        semanticAnalysisService,
        resourceProcessor,
        packageLoader,
        resourceMetadataExtractor,
        dependencyChainAnalyzer,
        gameplaySystemAnalyzer,
        resourceProvider,
        streamPipeline,
        dependencyPipelineAdapter
    } = options;

    // Create instances with correct constructor arguments
    const semanticService = semanticAnalysisService || new SemanticAnalysisService(databaseService);
    const resourceProc = resourceProcessor || new ResourceProcessor(databaseService.resources, logger);
    const packageLoad = packageLoader || new PackageLoader(databaseService.packages, logger);
    const metadataExtractor = resourceMetadataExtractor || new ResourceMetadataExtractor(databaseService, logger);
    const depChainAnalyzer = dependencyChainAnalyzer || new DependencyChainAnalyzer(databaseService);
    const gameplayAnalyzer = gameplaySystemAnalyzer || new GameplaySystemAnalyzer(databaseService);
    const resProvider = resourceProvider || new ConsolidatedResourceProvider();
    const streamPipe = streamPipeline || new ConsolidatedStreamPipeline();
    const depPipelineAdapter = dependencyPipelineAdapter || new EnhancedDependencyPipelineAdapter(databaseService);

    logger.info('Creating package analyzer');

    return new PackageAnalyzer(
        databaseService,
        semanticService,
        resourceProc,
        packageLoad,
        metadataExtractor,
        depChainAnalyzer,
        gameplayAnalyzer,
        resProvider,
        streamPipe,
        depPipelineAdapter
    );
}
