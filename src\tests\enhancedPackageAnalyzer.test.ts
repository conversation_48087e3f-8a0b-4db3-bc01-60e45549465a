/**
 * Tests for EnhancedPackageAnalyzer
 */

import { describe, it, beforeEach, afterEach, expect, vi, Mock } from 'vitest';
import { Readable } from 'stream';
import { PackageAnalyzer } from '../services/analysis/packageAnalyzer.js';
import { DatabaseService } from '../services/databaseService.js';
import { ResourceProcessor } from '../services/analysis/package/resourceProcessor.js';
import { PackageLoader } from '../services/analysis/package/packageLoader.js';
import { ResourceMetadataExtractor } from '../services/analysis/package/resourceMetadataExtractor.js';
import { SemanticAnalysisService } from '../services/analysis/semanticAnalysisService.js';
import { EnhancedDependencyChainAnalyzer } from '../services/analysis/semantic/enhancedDependencyChainAnalyzer.js';
import { GameplaySystemAnalyzer } from '../services/analysis/gameplay/GameplaySystemAnalyzer.js';
import { StreamingPackageReader } from '../services/analysis/package/streamingPackageReader.js';
import { Resource as ResourceInterface } from '../types/resource/interfaces.js';
import * as ResourceTypes from '../constants/resourceTypes.js';
import { Logger } from '../utils/logging/logger.js';

// Mock dependencies
vi.mock('../services/databaseService.js');
vi.mock('../services/analysis/package/resourceProcessor.js');
vi.mock('../services/analysis/package/packageLoader.js');
vi.mock('../services/analysis/package/resourceMetadataExtractor.js');
vi.mock('../services/analysis/semanticAnalysisService.js');
vi.mock('../services/analysis/semantic/enhancedDependencyChainAnalyzer.js');
vi.mock('../services/analysis/gameplay/GameplaySystemAnalyzer.js');
vi.mock('../services/analysis/package/streamingPackageReader.js');
vi.mock('../utils/logging/logger.js');

// Global.gc mock for garbage collection functionality
(global as any).gc = vi.fn();

describe('EnhancedPackageAnalyzer', () => {
  let analyzer: PackageAnalyzer;
  let mockDatabaseService: any;
  let mockResourceProcessor: any;
  let mockPackageLoader: any;
  let mockResourceMetadataExtractor: any;
  let mockSemanticAnalysisService: any;
  let mockDependencyChainAnalyzer: any;
  let mockGameplaySystemAnalyzer: any;
  
  // Mock package and resource data
  const mockPackagePath = '/path/to/package.package';
  const mockPackageId = 123;
  const mockPackageInfo = {
    name: 'TestPackage',
    path: mockPackagePath,
    hash: 'test-hash-123',
    size: 1024 * 1024, // 1MB
    lastModified: Date.now()
  };
  
  const mockResources = [
    { 
      key: { 
        type: ResourceTypes.RESOURCE_TYPE_TUNING, 
        group: 0, 
        instance: BigInt(123456789) 
      },
      metadata: {
        resourceType: 'Tuning',
        name: 'TestTuning',
        extractorUsed: 'tuning'
      },
      timestamp: Date.now()
    },
    { 
      key: { 
        type: ResourceTypes.RESOURCE_TYPE_SIMDATA, 
        group: 0, 
        instance: BigInt(123456789) 
      },
      metadata: {
        resourceType: 'SimData',
        name: 'TestSimData',
        extractorUsed: 'simdata'
      },
      timestamp: Date.now()
    }
  ];
  
  const mockEntries = [
    {
      type: ResourceTypes.RESOURCE_TYPE_TUNING,
      group: 0,
      instanceHi: 0,
      instanceLo: 123456789,
      offset: 0,
      fileSize: 1024,
      memSize: 2048,
      compressed: true
    },
    {
      type: ResourceTypes.RESOURCE_TYPE_SIMDATA,
      group: 0,
      instanceHi: 0,
      instanceLo: 123456789,
      offset: 1024,
      fileSize: 512,
      memSize: 1024,
      compressed: true
    }
  ];
  
  // Setup mocks before each test
  beforeEach(() => {
    // Create mock database service
    mockDatabaseService = {
      executeQuery: vi.fn().mockResolvedValue([{
        value: JSON.stringify({ impactScore: 75 })
      }]),
      dependencies: {
        getPackageDependencyCount: vi.fn().mockResolvedValue(42),
        getCrossPackageDependencyCount: vi.fn().mockResolvedValue(12)
      },
      checkTableExists: vi.fn().mockResolvedValue(true)
    };
    
    // Create mock resource processor
    mockResourceProcessor = {
      processEntry: vi.fn().mockImplementation(async (entry, packageId) => ({
        resourceId: 1000,
        resourceInfo: {
          key: entry.key,
          metadata: {
            resourceType: entry.key.type === ResourceTypes.RESOURCE_TYPE_TUNING ? 'Tuning' : 'SimData'
          }
        },
        resourceBuffer: Buffer.from('test data')
      }))
    };
    
    // Create mock package loader
    mockPackageLoader = {
      loadPackage: vi.fn().mockResolvedValue({
        packageId: mockPackageId,
        packageInfo: mockPackageInfo
      })
    };
    
    // Create mock resource metadata extractor
    mockResourceMetadataExtractor = {
      extractMetadata: vi.fn().mockImplementation(async (key, buffer, resourceId) => ({
        name: key.type === ResourceTypes.RESOURCE_TYPE_TUNING ? 'TestTuning' : 'TestSimData',
        extractorUsed: key.type === ResourceTypes.RESOURCE_TYPE_TUNING ? 'tuning' : 'simdata'
      }))
    };
    
    // Create mock semantic analysis service
    mockSemanticAnalysisService = {
      analyzePackageSemantics: vi.fn().mockResolvedValue(undefined),
      getPackageSemanticSummary: vi.fn().mockResolvedValue({
        resourceCount: 2,
        typeCounts: { 'Tuning': 1, 'SimData': 1 }
      })
    };
    
    // Create mock dependency chain analyzer
    mockDependencyChainAnalyzer = {
      initialize: vi.fn().mockResolvedValue(undefined),
      analyzeEnhancedDependencyChain: vi.fn().mockResolvedValue({
        root: { resourceId: 1000, children: [] },
        impactScore: 75,
        crossPackageDependencies: [],
        impactedGameplaySystems: ['needs', 'skills']
      }),
      dispose: vi.fn().mockResolvedValue(undefined)
    };
    
    // Create mock gameplay system analyzer
    mockGameplaySystemAnalyzer = {
      initialize: vi.fn().mockResolvedValue(undefined),
      analyzePackage: vi.fn().mockResolvedValue({
        systems: [
          { name: 'needs', impactScore: 80 },
          { name: 'skills', impactScore: 60 }
        ]
      }),
      dispose: vi.fn().mockResolvedValue(undefined)
    };
    
    // Mock StreamingPackageReader
    (StreamingPackageReader as any).mockImplementation(() => ({
      initialize: vi.fn().mockResolvedValue(undefined),
      getResourceEntries: vi.fn().mockResolvedValue(mockEntries),
      readResource: vi.fn().mockResolvedValue(Buffer.from('test resource data')),
      getDirectBufferThreshold: vi.fn().mockReturnValue(5 * 1024 * 1024),
      getChunkedProcessingThreshold: vi.fn().mockReturnValue(50 * 1024 * 1024),
      close: vi.fn().mockResolvedValue(undefined)
    }));
    
    // Create analyzer instance
    analyzer = new PackageAnalyzer(
      mockDatabaseService as any,
      mockResourceProcessor as any,
      mockPackageLoader as any,
      mockResourceMetadataExtractor as any,
      mockSemanticAnalysisService as any,
      mockDependencyChainAnalyzer as any,
      mockGameplaySystemAnalyzer as any
    );
  });
  
  afterEach(() => {
    vi.resetAllMocks();
  });
  
  it('should initialize correctly', async () => {
    await analyzer.initialize();
    
    expect(mockDependencyChainAnalyzer.initialize).toHaveBeenCalled();
    expect(mockGameplaySystemAnalyzer.initialize).toHaveBeenCalled();
  });
  
  it('should analyze a package with default options', async () => {
    await analyzer.initialize();
    
    const result = await analyzer.analyzePackage(mockPackagePath);
    
    // Check basic package info
    expect(result.id).toBe(mockPackageId);
    expect(result.name).toBe(mockPackageInfo.name);
    expect(result.path).toBe(mockPackageInfo.path);
    
    // Check that resources were processed
    expect(mockResourceProcessor.processEntry).toHaveBeenCalledTimes(mockEntries.length);
    
    // Check that metadata was extracted
    expect(mockResourceMetadataExtractor.extractMetadata).toHaveBeenCalledTimes(mockEntries.length);
    
    // Check that semantic analysis was performed
    expect(mockSemanticAnalysisService.analyzePackageSemantics).toHaveBeenCalledWith(mockPackageId);
    
    // Check that gameplay analysis was performed
    expect(mockGameplaySystemAnalyzer.analyzePackage).toHaveBeenCalledWith(mockPackageId);
    
    // Check enhanced analysis results
    expect(result.enhancedAnalysis).toBeDefined();
    expect(result.enhancedAnalysis?.dependencies.totalDependencies).toBe(42);
    expect(result.enhancedAnalysis?.dependencies.crossPackageDependencies).toBe(12);
    expect(result.enhancedAnalysis?.dependencies.impactedGameplaySystems).toContain('needs');
    expect(result.enhancedAnalysis?.dependencies.impactedGameplaySystems).toContain('skills');
    expect(result.enhancedAnalysis?.dependencies.highestImpactScore).toBe(75);
    
    // Check performance metrics
    expect(result.enhancedAnalysis?.performance).toBeDefined();
    expect(result.enhancedAnalysis?.performance.totalTime).toBeGreaterThan(0);
  });
  
  it('should analyze a package with custom options', async () => {
    await analyzer.initialize();
    
    const result = await analyzer.analyzePackage(mockPackagePath, {
      batchSize: 10,
      directBufferThreshold: 2 * 1024 * 1024, // 2MB
      maxDependencyDepth: 2,
      includaGameplayAnalysis: false,
      analyzeCrossPackageDependencies: false,
      enableHardwareAwareness: true,
      workloadIntensity: 'low'
    });
    
    // Check that package was analyzed
    expect(result.id).toBe(mockPackageId);
    expect(result.resources.length).toBe(mockEntries.length);
  });
  
  it('should handle errors during resource processing', async () => {
    await analyzer.initialize();
    
    // Make resource processor fail on the second resource
    mockResourceProcessor.processEntry.mockImplementation(async (entry, packageId) => {
      if (entry.key.type === ResourceTypes.RESOURCE_TYPE_SIMDATA) {
        throw new Error('Test processing error');
      }
      
      return {
        resourceId: 1000,
        resourceInfo: {
          key: entry.key,
          metadata: {
            resourceType: 'Tuning'
          }
        },
        resourceBuffer: Buffer.from('test data')
      };
    });
    
    // Should not throw but log the error
    const result = await analyzer.analyzePackage(mockPackagePath);
    
    // Should still have processed the first resource
    expect(result.resources.length).toBe(1);
  });
  
  it('should dispose resources properly', async () => {
    await analyzer.initialize();
    await analyzer.dispose();
    
    expect(mockDependencyChainAnalyzer.dispose).toHaveBeenCalled();
    expect(mockGameplaySystemAnalyzer.dispose).toHaveBeenCalled();
  });
});