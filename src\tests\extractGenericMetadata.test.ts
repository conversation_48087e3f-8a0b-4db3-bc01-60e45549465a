import { extractGenericMetadata } from '../services/analysis/extractors/extractGenericMetadata.js';
import { ResourceKey } from '../types/resource/interfaces.js';
import { DatabaseService } from '../services/databaseService.js';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import fs from 'fs';
import path from 'path';
import BinaryResourceType from '@s4tk/models/lib/enums/binary-resources.js';

// Mock the Package class since we can't import it directly
const mockPackage = {
  from: (buffer: Buffer) => {
    // Simple mock implementation that returns a package with some entries
    return {
      entries: [
        {
          key: { type: 0x0C772E27, group: 0, instance: 123 },
          value: { getBuffer: async () => Buffer.from('test') }
        },
        {
          key: { type: 0x6017E896, group: 0, instance: 456 },
          value: { getBuffer: async () => Buffer.from('test') }
        }
      ]
    };
  }
};

// Mock the database service
const mockDatabaseService = {
  saveParsedContent: vi.fn(),
} as unknown as DatabaseService;

describe('extractGenericMetadata', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should extract basic metadata from a text buffer', async () => {
    // Create a text buffer
    const textContent = 'This is a test text content for the generic extractor';
    const buffer = Buffer.from(textContent, 'utf8');

    // Create a resource key
    const key: ResourceKey = {
      type: BinaryResourceType.StringTable,
      group: 0n,
      instance: 0n
    };

    // Extract metadata
    const metadata = await extractGenericMetadata(key, buffer, 1, mockDatabaseService);

    // Check the extracted metadata
    expect(metadata.contentSnippet).toContain(textContent);
    expect(metadata.resourceType).toBe('STRING_TABLE');
    expect(metadata.isOfficialType).toBe(true);

    // Check that saveParsedContent was called
    expect(mockDatabaseService.saveParsedContent).toHaveBeenCalledTimes(1);
  });

  it('should extract basic metadata from a binary buffer', async () => {
    // Create a binary buffer
    const buffer = Buffer.from([0x44, 0x44, 0x53, 0x20, 0x7C, 0x00, 0x00, 0x00, 0x07, 0x10, 0x0A, 0x00, 0x00, 0x08, 0x00, 0x00]);

    // Create a resource key
    const key: ResourceKey = {
      type: BinaryResourceType.DdsImage,
      group: 0n,
      instance: 0n
    };

    // Extract metadata
    const metadata = await extractGenericMetadata(key, buffer, 1, mockDatabaseService);

    // Check the extracted metadata
    expect(metadata.contentSnippet).toContain('Binary DDS_IMAGE Data');
    expect(metadata.contentSnippet).toContain('Identified as: DDS Image');
    expect(metadata.resourceType).toBe('DDS_IMAGE');
    expect(metadata.isOfficialType).toBe(true);

    // Check that saveParsedContent was called
    expect(mockDatabaseService.saveParsedContent).toHaveBeenCalledTimes(1);
  });

  it('should handle unknown resource types', async () => {
    // Create a binary buffer
    const buffer = Buffer.from([0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F]);

    // Create a resource key with unknown type
    const key: ResourceKey = {
      type: 0x12345678,
      group: 0n,
      instance: 0n
    };

    // Extract metadata
    const metadata = await extractGenericMetadata(key, buffer, 1, mockDatabaseService);

    // Check the extracted metadata
    expect(metadata.contentSnippet).toContain('Binary TYPE_0x12345678 Data');
    expect(metadata.resourceType).toMatch(/TYPE_0x12345678/);
    expect(metadata.isOfficialType).toBe(false);

    // Check that saveParsedContent was called
    expect(mockDatabaseService.saveParsedContent).toHaveBeenCalledTimes(1);
  });

  it('should handle empty buffers', async () => {
    // Create an empty buffer
    const buffer = Buffer.alloc(0);

    // Create a resource key
    const key: ResourceKey = {
      type: BinaryResourceType.StringTable,
      group: 0n,
      instance: 0n
    };

    // Extract metadata
    const metadata = await extractGenericMetadata(key, buffer, 1, mockDatabaseService);

    // Check the extracted metadata
    expect(metadata.contentSnippet).toContain('Empty STRING_TABLE Data');
    expect(metadata.resourceType).toBe('STRING_TABLE');

    // Check that saveParsedContent was called
    expect(mockDatabaseService.saveParsedContent).toHaveBeenCalledTimes(1);
  });

  // Test with real Sims 4 package files if available
  it('should extract metadata from real Sims 4 package files', async () => {
    const modsPath = 'C:/Users/<USER>/OneDrive/Documents/Electronic Arts/The Sims 4/Mods';

    // Skip test if mods directory doesn't exist
    if (!fs.existsSync(modsPath)) {
      console.log('Skipping real package test - Mods directory not found');
      return;
    }

    // Find a package file to test with
    const findPackageFile = (dir: string): string | null => {
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          const found = findPackageFile(filePath);
          if (found) return found;
        } else if (file.endsWith('.package') || file.endsWith('.ts4script')) {
          return filePath;
        }
      }

      return null;
    };

    const packagePath = findPackageFile(modsPath);
    if (!packagePath) {
      console.log('Skipping real package test - No package files found');
      return;
    }

    console.log(`Testing with package file: ${packagePath}`);

    try {
      // Load the package using our mock
      const packageBuffer = fs.readFileSync(packagePath);
      const pkg = mockPackage.from(packageBuffer);

      // Test the first entry in the package
      if (pkg.entries.length > 0) {
        const entry = pkg.entries[0];
        const resource = entry.value;
        const buffer = await resource.getBuffer();

        const key: ResourceKey = {
          type: entry.key.type,
          group: BigInt(entry.key.group),
          instance: BigInt(entry.key.instance)
        };

        // Extract metadata
        const metadata = await extractGenericMetadata(key, buffer, 1, mockDatabaseService);

        // Check that we got some metadata
        expect(metadata).toBeDefined();
        expect(metadata.contentSnippet).toBeDefined();
        expect(metadata.resourceType).toBeDefined();

        // Check that saveParsedContent was called
        expect(mockDatabaseService.saveParsedContent).toHaveBeenCalledTimes(1);
      }
    } catch (error) {
      console.error('Error analyzing package:', error);
      // Don't fail the test if there's an error analyzing the package
    }
  });
});
