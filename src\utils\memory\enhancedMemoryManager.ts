/**
 * Enhanced Memory Manager
 *
 * This module provides an enhanced memory manager with advanced memory tracking,
 * resource monitoring, and automatic garbage collection capabilities.
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import os from 'os';
import { formatBytes } from '../formatting/formatUtils.js';

// Create a logger for this module
const logger = new Logger('EnhancedMemoryManager');

/**
 * Memory usage thresholds (in bytes)
 */
export interface MemoryThresholds {
    warning: number;
    critical: number;
    emergency: number;
}

/**
 * Memory manager options
 */
export interface EnhancedMemoryManagerOptions {
    thresholds?: Partial<MemoryThresholds>;
    autoGcEnabled?: boolean;
    trackingEnabled?: boolean;
    trackingIntervalMs?: number;
    detailedTracking?: boolean;
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Memory usage statistics
 */
export interface MemoryStats {
    heapUsed: number;
    heapTotal: number;
    rss: number;
    external: number;
    arrayBuffers: number;
    freeSystemMemory: number;
    totalSystemMemory: number;
    usedPercentage: number;
    timestamp: number;
}

/**
 * Resource usage statistics
 */
export interface ResourceStats {
    type: string;
    count: number;
    size: number;
    timestamp: number;
}

/**
 * Memory usage history entry
 */
interface MemoryHistoryEntry {
    stats: MemoryStats;
    resources: ResourceStats[];
    timestamp: number;
}

/**
 * Default memory thresholds
 */
const DEFAULT_THRESHOLDS: MemoryThresholds = {
    warning: 1.5 * 1024 * 1024 * 1024, // 1.5 GB
    critical: 2 * 1024 * 1024 * 1024,  // 2 GB
    emergency: 2.5 * 1024 * 1024 * 1024 // 2.5 GB
};

/**
 * Default options
 */
const DEFAULT_OPTIONS: EnhancedMemoryManagerOptions = {
    thresholds: DEFAULT_THRESHOLDS,
    autoGcEnabled: false,
    trackingEnabled: false,
    trackingIntervalMs: 30000, // 30 seconds
    detailedTracking: false,
    logLevel: 'info'
};

/**
 * Enhanced Memory Manager class
 * Implements the Singleton pattern to ensure only one memory manager exists
 */
export class EnhancedMemoryManager extends EventEmitter {
    private static instance: EnhancedMemoryManager;
    private options: Required<EnhancedMemoryManagerOptions>;
    private thresholds: MemoryThresholds;
    private trackingIntervalId?: NodeJS.Timeout;
    private memoryHistory: MemoryHistoryEntry[] = [];
    private resourceTrackers = new Map<string, { count: number, size: number }>();
    private isInitialized = false;
    private MAX_HISTORY_LENGTH = 100;

    /**
     * Private constructor to enforce Singleton pattern
     * @param options Memory manager options
     */
    private constructor(options: EnhancedMemoryManagerOptions = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners

        // Merge options with defaults
        this.options = {
            ...DEFAULT_OPTIONS,
            ...options,
            thresholds: {
                ...DEFAULT_THRESHOLDS,
                ...(options.thresholds || {})
            }
        } as Required<EnhancedMemoryManagerOptions>;

        this.thresholds = this.options.thresholds as MemoryThresholds;

        logger.info('Enhanced memory manager created');
    }

    /**
     * Get the EnhancedMemoryManager instance (Singleton pattern)
     * @param options Memory manager options (only used on first call)
     * @returns The EnhancedMemoryManager instance
     */
    public static getInstance(options?: EnhancedMemoryManagerOptions): EnhancedMemoryManager {
        if (!EnhancedMemoryManager.instance) {
            EnhancedMemoryManager.instance = new EnhancedMemoryManager(options);
        }
        return EnhancedMemoryManager.instance;
    }

    /**
     * Initialize the memory manager
     * @returns The memory manager instance (for chaining)
     */
    public initialize(): EnhancedMemoryManager {
        if (this.isInitialized) {
            logger.warn('Memory manager is already initialized');
            return this;
        }

        // Log initial memory usage
        this.logMemoryUsage();

        // Start tracking if enabled
        if (this.options.trackingEnabled) {
            this.startTracking();
        }

        // Register exit handler
        process.on('exit', () => {
            this.stopTracking();
            logger.info('Memory manager shutdown');
        });

        this.isInitialized = true;
        logger.info('Memory manager initialized');
        return this;
    }

    /**
     * Configure the memory manager
     * @param options Memory manager options
     * @returns The memory manager instance (for chaining)
     */
    public configure(options: EnhancedMemoryManagerOptions): EnhancedMemoryManager {
        // Update thresholds
        if (options.thresholds) {
            this.thresholds = {
                ...this.thresholds,
                ...options.thresholds
            };
        }

        // Update other options
        if (options.autoGcEnabled !== undefined) {
            this.options.autoGcEnabled = options.autoGcEnabled;
        }

        if (options.trackingIntervalMs !== undefined) {
            this.options.trackingIntervalMs = options.trackingIntervalMs;

            // Restart tracking if it's enabled
            if (this.options.trackingEnabled && this.trackingIntervalId) {
                this.stopTracking();
                this.startTracking();
            }
        }

        if (options.detailedTracking !== undefined) {
            this.options.detailedTracking = options.detailedTracking;
        }

        if (options.logLevel !== undefined) {
            this.options.logLevel = options.logLevel;
        }

        // Handle tracking toggle
        if (options.trackingEnabled !== undefined) {
            if (options.trackingEnabled && !this.options.trackingEnabled) {
                this.options.trackingEnabled = true;
                this.startTracking();
            } else if (!options.trackingEnabled && this.options.trackingEnabled) {
                this.options.trackingEnabled = false;
                this.stopTracking();
            }
        }

        logger.info('Memory manager configured', options);
        return this;
    }

    /**
     * Start memory usage tracking
     * @returns The memory manager instance (for chaining)
     */
    public startTracking(): EnhancedMemoryManager {
        if (this.trackingIntervalId) {
            logger.warn('Memory tracking is already enabled');
            return this;
        }

        this.options.trackingEnabled = true;

        // Start tracking
        this.trackingIntervalId = setInterval(() => {
            this.trackMemoryUsage();
        }, this.options.trackingIntervalMs);

        logger.info(`Memory tracking started (interval: ${this.options.trackingIntervalMs}ms)`);
        return this;
    }

    /**
     * Stop memory usage tracking
     * @returns The memory manager instance (for chaining)
     */
    public stopTracking(): EnhancedMemoryManager {
        if (!this.trackingIntervalId) {
            logger.warn('Memory tracking is not enabled');
            return this;
        }

        clearInterval(this.trackingIntervalId);
        this.trackingIntervalId = undefined;
        this.options.trackingEnabled = false;

        logger.info('Memory tracking stopped');
        return this;
    }

    /**
     * Track memory usage
     * @private
     */
    private trackMemoryUsage(): void {
        const memStats = this.getMemoryStats();
        const resourceStats = this.getResourceStats();

        // Add to history
        this.memoryHistory.push({
            stats: memStats,
            resources: resourceStats,
            timestamp: Date.now()
        });

        // Trim history if needed
        if (this.memoryHistory.length > this.MAX_HISTORY_LENGTH) {
            this.memoryHistory.shift();
        }

        // Check thresholds
        if (memStats.heapUsed > this.thresholds.emergency) {
            logger.error(`EMERGENCY: Memory usage is extremely high (${formatBytes(memStats.heapUsed)})`);
            this.logMemoryUsage('error');

            // Force garbage collection if enabled
            if (this.options.autoGcEnabled) {
                this.forceGarbageCollection();
            }

            // Emit emergency event
            this.emit('memoryEmergency', memStats);
        } else if (memStats.heapUsed > this.thresholds.critical) {
            logger.error(`CRITICAL: Memory usage is very high (${formatBytes(memStats.heapUsed)})`);
            this.logMemoryUsage('error');

            // Force garbage collection if enabled
            if (this.options.autoGcEnabled) {
                this.forceGarbageCollection();
            }

            // Emit critical event
            this.emit('memoryCritical', memStats);
        } else if (memStats.heapUsed > this.thresholds.warning) {
            logger.warn(`WARNING: Memory usage is high (${formatBytes(memStats.heapUsed)})`);
            this.logMemoryUsage('warn');

            // Suggest garbage collection if enabled
            if (this.options.autoGcEnabled) {
                this.suggestGarbageCollection();
            }

            // Emit warning event
            this.emit('memoryWarning', memStats);
        }

        // Emit update event
        this.emit('memoryUpdate', {
            stats: memStats,
            resources: resourceStats
        });
    }

    /**
     * Get current memory usage statistics
     * @returns Memory usage statistics
     */
    public getMemoryStats(): MemoryStats {
        const memUsage = process.memoryUsage();
        const freeSystemMemory = os.freemem();
        const totalSystemMemory = os.totalmem();

        return {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            rss: memUsage.rss,
            external: memUsage.external,
            arrayBuffers: memUsage.arrayBuffers || 0,
            freeSystemMemory,
            totalSystemMemory,
            usedPercentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
            timestamp: Date.now()
        };
    }

    /**
     * Get the current memory pressure (ratio of used heap to total heap)
     * @returns Memory pressure as a value between 0 and 1
     */
    public getMemoryPressure(): number {
        const memoryUsage = process.memoryUsage();
        const heapUsed = memoryUsage.heapUsed;
        const heapTotal = memoryUsage.heapTotal;
        return heapUsed / heapTotal;
    }

    /**
     * Get formatted memory usage information
     * @returns Formatted string with memory usage details
     */
    public getMemoryUsageInfo(): string {
        const memoryUsage = process.memoryUsage();
        const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        const rss = Math.round(memoryUsage.rss / 1024 / 1024);
        return `Heap: ${heapUsedMB}MB/${heapTotalMB}MB, RSS: ${rss}MB`;
    }

    /**
     * Adjust batch size based on memory pressure
     * @param currentBatchSize Current batch size
     * @param minBatchSize Minimum batch size
     * @param maxBatchSize Maximum batch size
     * @returns Adjusted batch size
     */
    public adjustBatchSize(currentBatchSize: number, minBatchSize: number = 10, maxBatchSize: number = 200): number {
        const memoryPressure = this.getMemoryPressure();

        if (memoryPressure > 0.8) {
            // High memory pressure, reduce batch size
            const newBatchSize = Math.max(minBatchSize, Math.floor(currentBatchSize * 0.5));
            logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%), reducing batch size from ${currentBatchSize} to ${newBatchSize}`);
            return newBatchSize;
        } else if (memoryPressure < 0.5 && currentBatchSize < maxBatchSize) {
            // Low memory pressure, increase batch size
            const newBatchSize = Math.min(maxBatchSize, Math.floor(currentBatchSize * 1.5));
            logger.info(`Low memory pressure (${(memoryPressure * 100).toFixed(1)}%), increasing batch size from ${currentBatchSize} to ${newBatchSize}`);
            return newBatchSize;
        }

        // Memory pressure is acceptable, keep current batch size
        return currentBatchSize;
    }

    /**
     * Get resource usage statistics
     * @returns Resource usage statistics
     */
    public getResourceStats(): ResourceStats[] {
        return Array.from(this.resourceTrackers.entries()).map(([type, stats]) => ({
            type,
            count: stats.count,
            size: stats.size,
            timestamp: Date.now()
        }));
    }

    /**
     * Log current memory usage
     * @param level Log level
     */
    public logMemoryUsage(level: 'debug' | 'info' | 'warn' | 'error' = 'info'): void {
        const memStats = this.getMemoryStats();

        const message = [
            `Memory Usage:`,
            `  Heap Used: ${formatBytes(memStats.heapUsed)} (${memStats.usedPercentage}% of heap)`,
            `  Heap Total: ${formatBytes(memStats.heapTotal)}`,
            `  RSS: ${formatBytes(memStats.rss)}`,
            `  External: ${formatBytes(memStats.external)}`,
            `  Array Buffers: ${formatBytes(memStats.arrayBuffers)}`,
            `  System Memory: ${formatBytes(memStats.freeSystemMemory)} free of ${formatBytes(memStats.totalSystemMemory)}`
        ].join('\n');

        switch (level) {
            case 'debug':
                logger.debug(message);
                break;
            case 'warn':
                logger.warn(message);
                break;
            case 'error':
                logger.error(message);
                break;
            default:
                logger.info(message);
        }
    }

    /**
     * Register a resource for tracking
     * @param type Resource type
     * @param size Resource size in bytes
     * @returns The memory manager instance (for chaining)
     */
    public trackResource(type: string, size: number): EnhancedMemoryManager {
        if (!this.resourceTrackers.has(type)) {
            this.resourceTrackers.set(type, { count: 0, size: 0 });
        }

        const tracker = this.resourceTrackers.get(type)!;
        tracker.count++;
        tracker.size += size;

        return this;
    }

    /**
     * Unregister a resource from tracking
     * @param type Resource type
     * @param size Resource size in bytes
     * @returns The memory manager instance (for chaining)
     */
    public untrackResource(type: string, size: number): EnhancedMemoryManager {
        if (!this.resourceTrackers.has(type)) {
            logger.warn(`Resource type ${type} is not being tracked`);
            return this;
        }

        const tracker = this.resourceTrackers.get(type)!;
        tracker.count = Math.max(0, tracker.count - 1);
        tracker.size = Math.max(0, tracker.size - size);

        return this;
    }

    /**
     * Suggest garbage collection
     * @returns True if garbage collection was suggested
     */
    public suggestGarbageCollection(): boolean {
        if (global.gc) {
            try {
                logger.debug('Suggesting garbage collection');
                global.gc();
                return true;
            } catch (error) {
                logger.error('Error suggesting garbage collection', error);
            }
        } else {
            logger.debug('Garbage collection not available. Run with --expose-gc flag to enable.');
        }

        return false;
    }

    /**
     * Force garbage collection
     * @returns True if garbage collection was forced
     */
    public forceGarbageCollection(): boolean {
        if (global.gc) {
            try {
                logger.info('Forcing garbage collection');
                global.gc();
                global.gc(); // Run twice for better results
                return true;
            } catch (error) {
                logger.error('Error forcing garbage collection', error);
            }
        } else {
            logger.warn('Garbage collection not available. Run with --expose-gc flag to enable.');
        }

        return false;
    }

    /**
     * Get memory usage history
     * @returns Memory usage history
     */
    public getMemoryHistory(): MemoryHistoryEntry[] {
        return [...this.memoryHistory];
    }

    /**
     * Clear memory usage history
     * @returns The memory manager instance (for chaining)
     */
    public clearMemoryHistory(): EnhancedMemoryManager {
        this.memoryHistory = [];
        return this;
    }
}

// Export the EnhancedMemoryManager class
export default EnhancedMemoryManager;
