import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { Logger } from '../utils/logging/logger.js';
import { PackageRepository } from './database/PackageRepository.js';
import { ResourceRepository } from './database/ResourceRepository.js';
import { MetadataRepository } from './database/MetadataRepository.js';
import { DependencyRepository } from './database/DependencyRepository.js';
import { ParsedContentRepository } from './database/ParsedContentRepository.js';
import { OverrideRepository } from './database/OverrideRepository.js';
import { SimDataSchemaRepository } from './database/simDataSchemaRepository.js';
import { ConflictRepository } from './database/ConflictRepository.js';
import { ScriptAnalysisRepository } from './database/ScriptAnalysisRepository.js';
import { ResourceSignatureRepository } from './database/ResourceSignatureRepository.js';
import { DatabaseMigrationService } from './database/DatabaseMigrationService.js';
import { ContentAddressableStorage } from './storage/ContentAddressableStorage.js';
import { EnhancedMemoryManager } from '../utils/memory/enhancedMemoryManager.js';
import { AdaptiveProcessingManager } from './analysis/adaptive/AdaptiveProcessingManager.js';
import {
    PackageInfo,
    ResourceInfo,
    MetadataInfo,
    DependencyInfo,
    ParsedContentInfo,
    OverrideInfo
} from '../types/database.js';
import { ConflictInfo } from '../types/conflict/index.js';

/**
 * Minimal database connection manager with proper memory management
 * Following better-sqlite3 best practices for memory efficiency
 */
class DatabaseConnectionPool {
    private connection: Database.Database;
    private logger: Logger;
    private walCheckpointInterval: NodeJS.Timeout | null = null;

    /**
     * Create a new database connection with minimal memory footprint
     * @param dbPath Path to the database file
     * @param logger Logger instance
     */
    constructor(dbPath: string, logger: Logger) {
        this.logger = logger;

        // Create a single connection - better-sqlite3 is designed for single connection use
        this.connection = new Database(dbPath, {
            // Disable verbose logging to reduce memory overhead
            verbose: undefined
        });

        // Configure for minimal memory usage following better-sqlite3 best practices
        this.connection.pragma('journal_mode = WAL'); // WAL mode for performance
        this.connection.pragma('synchronous = NORMAL'); // Balance safety and speed
        this.connection.pragma('foreign_keys = OFF'); // Disable for initialization speed

        // Minimal memory settings for low-end systems
        this.connection.pragma('mmap_size = 67108864'); // 64MB (reduced from 128MB)
        this.connection.pragma('cache_size = -2000'); // 2MB cache (minimal but functional)
        this.connection.pragma('temp_store = MEMORY'); // Use memory for temp storage
        this.connection.pragma('page_size = 4096'); // Standard page size

        // Setup periodic WAL checkpointing to prevent memory issues
        this.setupWalCheckpointing();

        this.logger.info(`Created minimal database connection with 64MB memory limit`);
    }

    /**
     * Setup periodic WAL checkpointing to prevent memory issues
     * Following better-sqlite3 documentation recommendations
     * @private
     */
    private setupWalCheckpointing(): void {
        // Checkpoint WAL every 30 seconds to prevent memory buildup
        this.walCheckpointInterval = setInterval(() => {
            try {
                // Check if WAL file exists and is getting large
                const walInfo = this.connection.pragma('wal_checkpoint(PASSIVE)', { simple: true });

                // If WAL has accumulated pages, force a checkpoint
                if (walInfo > 1000) { // More than 1000 pages (~4MB)
                    this.logger.debug(`WAL has ${walInfo} pages, forcing checkpoint`);
                    this.connection.pragma('wal_checkpoint(RESTART)');
                }
            } catch (error) {
                this.logger.error('Error during WAL checkpoint:', error);
            }
        }, 30000).unref(); // 30 seconds, unref to not keep process alive
    }

    /**
     * Get a connection for read operations
     * @returns The database connection
     */
    public getReadConnection(): Database.Database {
        if (!this.connection) {
            throw new Error('Database connection is not available (connection was closed)');
        }
        return this.connection;
    }

    /**
     * Get a connection for write operations
     * @returns The database connection
     */
    public getWriteConnection(): Database.Database {
        if (!this.connection) {
            throw new Error('Database connection is not available (connection was closed)');
        }
        return this.connection;
    }

    /**
     * Close the database connection properly
     */
    public close(): void {
        if (this.connection) {
            try {
                // Clear WAL checkpoint interval
                if (this.walCheckpointInterval) {
                    clearInterval(this.walCheckpointInterval);
                    this.walCheckpointInterval = null;
                }

                // Final WAL checkpoint before closing
                this.connection.pragma('wal_checkpoint(TRUNCATE)');

                // Close the connection
                this.connection.close();

                // Clear reference to help garbage collection
                (this.connection as any) = null;

                this.logger.info('Closed database connection and cleared references');
            } catch (error) {
                this.logger.error('Error closing database connection:', error);
            }
        }
    }
}


// Define the path to the database file
const dbPath = path.resolve(process.cwd(), 'data', 'mod_analysis.db');

class DatabaseService {
    private connectionPool: DatabaseConnectionPool;
    private logger: Logger;
    private static instance: DatabaseService;
    private dbPath: string;
    private migrationService: DatabaseMigrationService;
    private contentStorage: ContentAddressableStorage;
    private memoryManager: EnhancedMemoryManager;
    private adaptiveProcessingManager: AdaptiveProcessingManager;

    // Repositories (using lazy getters to prevent memory allocation during initialization)
    private _metadata: MetadataRepository | null = null;
    private _dependencies: DependencyRepository | null = null;
    private _parsedContent: ParsedContentRepository | null = null;
    private _overrides: OverrideRepository | null = null;
    private _simDataSchemas: SimDataSchemaRepository | null = null;
    private _conflicts: ConflictRepository | null = null;
    private _scriptAnalysis: ScriptAnalysisRepository | null = null;
    private _signatures: ResourceSignatureRepository | null = null;

    // Getter for the primary database connection (for backward compatibility)
    private get db(): Database.Database {
        return this.connectionPool.getWriteConnection();
    }


    constructor(loggerOrDbPath?: Logger | string, customLogger?: Logger) {
        // Handle different constructor signatures
        let dbPathToUse: string | ':memory:' = dbPath;

        if (typeof loggerOrDbPath === 'string') {
            // If first argument is a string, it's the database path
            dbPathToUse = loggerOrDbPath;
            this.logger = customLogger || new Logger('DatabaseService');
        } else {
            // If first argument is a Logger or undefined
            this.logger = loggerOrDbPath || new Logger('DatabaseService');
        }

        // Store the database path
        this.dbPath = dbPathToUse;

        // Create the database directory if needed
        if (dbPathToUse !== ':memory:') {
            const dataDir = path.dirname(dbPathToUse);
            if (!fs.existsSync(dataDir)) {
                try {
                    fs.mkdirSync(dataDir, { recursive: true });
                    this.logger.info(`Created data directory: ${dataDir}`);
                } catch (err) {
                    this.logger.error(`Error creating data directory ${dataDir}:`, err);
                    throw new Error(`Failed to create data directory for SQLite database: ${err}`);
                }
            }
        }

        // Initialize connection pool
        if (dbPathToUse === ':memory:') {
            this.logger.info('Using in-memory database');
            // Create a connection for in-memory database
            this.connectionPool = new DatabaseConnectionPool(':memory:', this.logger);
        } else {
            // Create a connection for file-based database
            this.connectionPool = new DatabaseConnectionPool(dbPathToUse, this.logger);
        }

        // Initialize memory management systems
        this.memoryManager = EnhancedMemoryManager.getInstance();
        this.adaptiveProcessingManager = AdaptiveProcessingManager.getInstance();

        // Initialize repositories lazily (will be created when first accessed)
        this.initializeRepositories();

        // Skip migration service and content storage to minimize memory usage during initialization
        this.migrationService = null as any;
        this.contentStorage = null as any;
    }

    /**
     * Initialize repositories lazily
     * @private
     */
    private initializeRepositories(): void {
        // All repositories are now lazy-loaded via getters
        // No initialization needed here - saves memory during startup
        this.logger.debug('Repository initialization deferred for memory efficiency');
    }

    /**
     * Lazy getter for packages repository
     */
    public get packages(): PackageRepository {
        if (!this._packages) {
            this._packages = new PackageRepository(this.db, this.logger);
        }
        return this._packages;
    }
    public set packages(value: PackageRepository) {
        this._packages = value;
    }
    private _packages: PackageRepository | null = null;

    /**
     * Lazy getter for resources repository
     */
    public get resources(): ResourceRepository {
        if (!this._resources) {
            this._resources = new ResourceRepository(this.db, this.logger);
        }
        return this._resources;
    }
    public set resources(value: ResourceRepository) {
        this._resources = value;
    }
    private _resources: ResourceRepository | null = null;

    /**
     * Lazy getter for metadata repository
     */
    public get metadata(): MetadataRepository {
        if (!this._metadata) {
            this._metadata = new MetadataRepository(this.db, this.logger);
        }
        return this._metadata;
    }
    public set metadata(value: MetadataRepository) {
        this._metadata = value;
    }

    /**
     * Lazy getter for dependencies repository
     */
    public get dependencies(): DependencyRepository {
        if (!this._dependencies) {
            this._dependencies = new DependencyRepository(this.db, this.logger);
        }
        return this._dependencies;
    }
    public set dependencies(value: DependencyRepository) {
        this._dependencies = value;
    }

    /**
     * Lazy getter for parsedContent repository
     */
    public get parsedContent(): ParsedContentRepository {
        if (!this._parsedContent) {
            this._parsedContent = new ParsedContentRepository(this.db, this.logger);
        }
        return this._parsedContent;
    }
    public set parsedContent(value: ParsedContentRepository) {
        this._parsedContent = value;
    }

    /**
     * Lazy getter for overrides repository
     */
    public get overrides(): OverrideRepository {
        if (!this._overrides) {
            this._overrides = new OverrideRepository(this.db, this.logger);
        }
        return this._overrides;
    }
    public set overrides(value: OverrideRepository) {
        this._overrides = value;
    }

    /**
     * Lazy getter for simDataSchemas repository
     */
    public get simDataSchemas(): SimDataSchemaRepository {
        if (!this._simDataSchemas) {
            this._simDataSchemas = new SimDataSchemaRepository(this.db, this.logger);
        }
        return this._simDataSchemas;
    }
    public set simDataSchemas(value: SimDataSchemaRepository) {
        this._simDataSchemas = value;
    }

    /**
     * Lazy getter for conflicts repository
     */
    public get conflicts(): ConflictRepository {
        if (!this._conflicts) {
            this._conflicts = new ConflictRepository(this.db);
        }
        return this._conflicts;
    }
    public set conflicts(value: ConflictRepository) {
        this._conflicts = value;
    }

    /**
     * Lazy getter for scriptAnalysis repository
     */
    public get scriptAnalysis(): ScriptAnalysisRepository {
        if (!this._scriptAnalysis) {
            this._scriptAnalysis = new ScriptAnalysisRepository(this.db, this.logger);
        }
        return this._scriptAnalysis;
    }
    public set scriptAnalysis(value: ScriptAnalysisRepository) {
        this._scriptAnalysis = value;
    }

    /**
     * Lazy getter for signatures repository
     */
    public get signatures(): ResourceSignatureRepository {
        if (!this._signatures) {
            this._signatures = new ResourceSignatureRepository(this.db, this.logger);
        }
        return this._signatures;
    }
    public set signatures(value: ResourceSignatureRepository) {
        this._signatures = value;
    }

    /**
     * Get the singleton instance of DatabaseService
     * @param customLogger Optional custom logger to use
     * @returns The singleton DatabaseService instance
     */
    public static getInstance(customLogger?: Logger): DatabaseService {
        if (!DatabaseService.instance) {
            const logger = customLogger || new Logger('DatabaseService');
            DatabaseService.instance = new DatabaseService(logger);
        }
        return DatabaseService.instance;
    }

    public async initialize(): Promise<void> {
        console.log('Initializing database and creating tables...');
        this.logger.info('Initializing database and creating tables...');

        try {
            // Monitor memory pressure during initialization
            const initialMemoryStats = this.memoryManager.getMemoryStats();
            this.logger.info(`Initial memory usage: ${Math.round(initialMemoryStats.usedPercentage)}% (${Math.round(initialMemoryStats.heapUsed / 1024 / 1024)}MB)`);

            // Create tables if they don't exist (with memory monitoring)
            await this.createTablesWithMemoryManagement();

            // Skip all heavy initialization for maximum memory efficiency
            console.log('Skipping migration and content storage for memory efficiency...');
            this.logger.info('Minimal initialization - basic schema only');

            // Register exit handlers to ensure proper cleanup
            this.registerExitHandlers();

            // Final memory check
            const finalMemoryStats = this.memoryManager.getMemoryStats();
            this.logger.info(`Final memory usage: ${Math.round(finalMemoryStats.usedPercentage)}% (${Math.round(finalMemoryStats.heapUsed / 1024 / 1024)}MB)`);

            console.log('Database initialized successfully.');
            this.logger.info('Database initialized successfully.');
        } catch (error) {
            console.error('Error initializing database:', error);
            this.logger.error('Error initializing database:', error);
            throw error;
        }
    }

    /**
     * Create tables with memory management and adaptive processing
     * @private
     */
    private async createTablesWithMemoryManagement(): Promise<void> {
        // Create only essential tables for memory efficiency
        const tables = [
            {
                name: 'Packages',
                sql: `CREATE TABLE IF NOT EXISTS Packages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    path TEXT UNIQUE NOT NULL,
                    hash TEXT UNIQUE NOT NULL,
                    size INTEGER NOT NULL,
                    lastModified INTEGER NOT NULL
                );`
            },
            {
                name: 'Resources',
                sql: `CREATE TABLE IF NOT EXISTS Resources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    packageId INTEGER NOT NULL,
                    type INTEGER NOT NULL,
                    "group" TEXT NOT NULL,
                    instance TEXT NOT NULL,
                    hash TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    offset INTEGER NOT NULL,
                    contentSnippet TEXT,
                    resourceType TEXT NOT NULL,
                    FOREIGN KEY (packageId) REFERENCES Packages(id),
                    UNIQUE(packageId, type, "group", instance)
                );`
            }
        ];

        // Create tables one by one with memory monitoring
        for (const table of tables) {
            // Check memory pressure before each table
            const memoryPressure = this.memoryManager.getMemoryPressure();
            if (memoryPressure > 0.8) {
                this.logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%) before creating ${table.name} table, triggering cleanup`);
                await this.memoryManager.forceCleanup();

                // Wait a bit for cleanup to take effect
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            console.log(`Creating ${table.name} table...`);
            this.logger.debug(`Creating ${table.name} table...`);

            try {
                this.db.exec(table.sql);
                this.logger.debug(`Successfully created ${table.name} table`);
            } catch (error) {
                this.logger.error(`Error creating ${table.name} table:`, error);
                throw error;
            }
        }

        // Create indices with memory monitoring
        await this.createIndicesWithMemoryManagement();

        console.log('Finished creating tables.');
        this.logger.info('Finished creating tables.');
    }

    /**
     * Create database indices with memory management
     * @private
     */
    private async createIndicesWithMemoryManagement(): Promise<void> {
        // Create only essential indices for memory efficiency
        const indices = [
            'CREATE INDEX IF NOT EXISTS idx_resources_packageId ON Resources(packageId);',
            'CREATE INDEX IF NOT EXISTS idx_resources_tgi ON Resources(type, "group", instance);'
        ];

        for (const indexSql of indices) {
            // Check memory pressure before each index
            const memoryPressure = this.memoryManager.getMemoryPressure();
            if (memoryPressure > 0.8) {
                this.logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%) before creating index, triggering cleanup`);
                await this.memoryManager.forceCleanup();

                // Wait a bit for cleanup to take effect
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            try {
                this.db.exec(indexSql);
            } catch (error) {
                this.logger.error(`Error creating index: ${indexSql}`, error);
                // Continue with other indices
            }
        }

        this.logger.debug('Finished creating indices.');
    }

    private async createTables() {
        // Packages table (handled by PackageRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS Packages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                hash TEXT UNIQUE NOT NULL,
                size INTEGER NOT NULL,
                lastModified INTEGER NOT NULL
            );
        `);
        console.log('Creating Packages table...');

        // Resources table (handled by ResourceRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS Resources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                packageId INTEGER NOT NULL,
                type INTEGER NOT NULL,
                "group" TEXT NOT NULL,
                instance TEXT NOT NULL,
                hash TEXT NOT NULL,
                size INTEGER NOT NULL,
                offset INTEGER NOT NULL,
                contentSnippet TEXT,
                resourceType TEXT NOT NULL,
                FOREIGN KEY (packageId) REFERENCES Packages(id),
                UNIQUE(packageId, type, "group", instance)
            );
        `);
        console.log('Creating Resources table...');

        // Add indices for performance
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_resources_packageId ON Resources(packageId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_resources_tgi ON Resources(type, "group", instance);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_resources_hash ON Resources(hash);`);


        // Metadata table (handled by MetadataRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS Metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                resourceId INTEGER NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                FOREIGN KEY (resourceId) REFERENCES Resources(id),
                UNIQUE(resourceId, key)
            );
        `);
        console.log('Creating Metadata table...');
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_metadata_resourceId ON Metadata(resourceId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_metadata_key ON Metadata(key);`);


        // Dependencies table (handled by DependencyRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS Dependencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sourceResourceId INTEGER NOT NULL,
                targetType INTEGER NOT NULL,
                targetGroup TEXT NOT NULL,
                targetInstance TEXT NOT NULL,
                referenceType TEXT DEFAULT 'Unknown', -- Added default
                timestamp INTEGER DEFAULT (strftime('%s', 'now')), -- Added default timestamp
                FOREIGN KEY (sourceResourceId) REFERENCES Resources(id)
            );
        `);
        console.log('Creating Dependencies table...');
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_dependencies_sourceResourceId ON Dependencies(sourceResourceId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_dependencies_targetTgi ON Dependencies(targetType, targetGroup, targetInstance);`);

        // ParsedContent table (handled by ParsedContentRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS ParsedContent (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                resourceId INTEGER NOT NULL UNIQUE,
                contentType TEXT NOT NULL,
                content TEXT NOT NULL,
                FOREIGN KEY (resourceId) REFERENCES Resources(id)
            );
        `);
        console.log('Creating ParsedContent table...');
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_parsedcontent_resourceId ON ParsedContent(resourceId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_parsedcontent_contentType ON ParsedContent(contentType);`);

        // Overrides table (handled by OverrideRepository, but created here)
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS Overrides (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                packageId INTEGER NOT NULL,
                overridingResourceId INTEGER NOT NULL,
                overriddenTGI TEXT NOT NULL,
                overriddenResourceId INTEGER,
                FOREIGN KEY (packageId) REFERENCES Packages(id),
                FOREIGN KEY (overridingResourceId) REFERENCES Resources(id),
                UNIQUE(packageId, overridingResourceId, overriddenTGI)
            );
        `);
        console.log('Creating Overrides table...');
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_overrides_packageId ON Overrides(packageId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_overrides_overridingResourceId ON Overrides(overridingResourceId);`);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_overrides_overriddenTGI ON Overrides(overriddenTGI);`);


        console.log('Finished creating tables.');
    }

    // savePackage, saveResource, saveMetadata, saveParsedContent, saveDependencies, saveDependency, findResourceByTGI, getResourceIdByTGI, getResourceById, getResourcesByPackageId, findResourcesByTypeInPackage, getMetadataValue, getDependencies, getDependenciesForPackage, debugGetResourceByInstanceString, getPackageIdByPath, getPackageByHash, saveOverride methods are now in repositories.

    /**
     * Executes a series of database operations within a single transaction.
     * @param callback The function containing the database operations to execute.
     */
    public executeTransaction(callback: () => void): void {
        const transaction = this.db.transaction(callback);
        try {
            transaction();
        } catch (error) {
            this.logger.error('Transaction failed:', error);
            throw error; // Re-throw the error after logging
        }
    }

    /**
     * Execute a SQL query with parameters
     * @param query The SQL query to execute
     * @param params Optional parameters for the query
     * @returns The query result
     */
    public async executeQuery(query: string, params: any[] = []): Promise<any> {
        try {
            // Check if this is a SELECT query
            const isSelect = query.trim().toUpperCase().startsWith('SELECT');

            if (isSelect) {
                // For SELECT queries, use all() to get results from a read connection
                const db = this.connectionPool.getReadConnection();
                if (params.length === 0) {
                    return db.prepare(query).all();
                } else {
                    return db.prepare(query).all(...params);
                }
            } else {
                // For non-SELECT queries (CREATE, INSERT, UPDATE, DELETE), use run() on the write connection
                const db = this.connectionPool.getWriteConnection();
                if (params.length === 0) {
                    return db.prepare(query).run();
                } else {
                    return db.prepare(query).run(...params);
                }
            }
        } catch (error) {
            this.logger.error(`Query failed: ${query}`, error);
            throw error;
        }
    }

    /**
     * Execute a SQL query with parameters (non-SELECT)
     * @param query The SQL query to execute
     * @param params Optional parameters for the query
     * @returns The query result
     */
    public run(query: string, params: any[] = []): any {
        try {
            // Always use the write connection for non-SELECT queries
            const db = this.connectionPool.getWriteConnection();
            if (params.length === 0) {
                return db.prepare(query).run();
            } else {
                return db.prepare(query).run(...params);
            }
        } catch (error) {
            this.logger.error(`Run query failed: ${query}`, error);
            throw error;
        }
    }

    /**
     * Execute a SQL query with parameters (SELECT)
     * @param query The SQL query to execute
     * @param params Optional parameters for the query
     * @returns The query result
     */
    public all(query: string, params: any[] = []): any {
        try {
            // Use a read connection for SELECT queries
            const db = this.connectionPool.getReadConnection();
            if (params.length === 0) {
                return db.prepare(query).all();
            } else {
                return db.prepare(query).all(...params);
            }
        } catch (error) {
            this.logger.error(`All query failed: ${query}`, error);
            throw error;
        }
    }

    /**
     * Check if a table exists in the database
     * @param tableName The name of the table to check
     * @returns True if the table exists, false otherwise
     */
    public async checkTableExists(tableName: string): Promise<boolean> {
        try {
            const result = await this.executeQuery(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                [tableName]
            );
            return result.length > 0;
        } catch (error) {
            this.logger.error(`Failed to check if table ${tableName} exists:`, error);
            return false;
        }
    }

    /**
     * Close the database connection and clean up resources
     * This should be called when the database is no longer needed
     *
     * @param removeHandlers Whether to remove exit handlers (default: true)
     * @returns Promise that resolves when cleanup is complete
     */
    close(removeHandlers: boolean = true): void {
        try {
            // Remove exit handlers if requested
            if (removeHandlers) {
                this.removeExitHandlers();
            }

            // Close the database connection pool
            if (this.connectionPool) {
                this.logger.info('Closing database connection pool');
                this.connectionPool.close();
                this.logger.info('Database connection pool closed');
            }

            // Clean up any other resources
            // (Add any additional cleanup here if needed)

        } catch (error) {
            this.logger.error('Error closing database connections:', error);
        }
    }

    /**
     * Clear all tables in the database
     * This is useful for testing to prevent memory buildup
     */
    public async clearAllTables(): Promise<void> {
        try {
            const db = this.connectionPool.getWriteConnection();

            // Get all table names
            const tables = db.prepare(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            `).all() as { name: string }[];

            // Delete all data from each table
            for (const table of tables) {
                db.prepare(`DELETE FROM ${table.name}`).run();
            }

            // Run vacuum to reclaim space
            db.pragma('vacuum');

            this.logger.info(`Cleared all tables (${tables.length} tables)`);

            // Force garbage collection
            if (global.gc) {
                global.gc();
            }
        } catch (error) {
            this.logger.error('Error clearing tables:', error);
        }
    }

    /**
     * Register process exit handlers to ensure the database is closed properly
     * This helps prevent memory leaks and database corruption
     *
     * This implementation uses a more robust approach for Electron apps:
     * 1. Stores handler references so they can be removed later
     * 2. Uses once() for critical handlers to prevent duplicate execution
     * 3. Implements proper cleanup to avoid memory leaks
     */
    private exitHandlers: { event: string, handler: (...args: any[]) => void }[] = [];

    registerExitHandlers(): void {
        // Clean up any previously registered handlers
        this.removeExitHandlers();

        // Create handlers with proper binding to this instance
        const exitHandler = () => {
            this.logger.info('Process exit detected, closing database');
            this.close();
        };

        const sigintHandler = () => {
            this.logger.info('SIGINT received, closing database');
            this.close();
            // Don't call process.exit() here - let Electron handle it
            // This prevents issues with the Electron main process
        };

        const sigtermHandler = () => {
            this.logger.info('SIGTERM received, closing database');
            this.close();
            // Don't call process.exit() here - let Electron handle it
        };

        const uncaughtExceptionHandler = (error: Error) => {
            this.logger.error('Uncaught exception:', error);
            // Don't close database on uncaught exceptions - let the system handle recovery
            // Only close on actual process termination signals
            // this.close();
        };

        // Register handlers using once() for critical events
        process.once('exit', exitHandler);
        process.on('SIGINT', sigintHandler);
        process.on('SIGTERM', sigtermHandler);
        process.on('uncaughtException', uncaughtExceptionHandler);

        // Store references for later removal
        this.exitHandlers = [
            { event: 'exit', handler: exitHandler },
            { event: 'SIGINT', handler: sigintHandler },
            { event: 'SIGTERM', handler: sigtermHandler },
            { event: 'uncaughtException', handler: uncaughtExceptionHandler }
        ];

        this.logger.info('Exit handlers registered');
    }

    /**
     * Remove previously registered exit handlers
     * This prevents memory leaks when creating multiple instances
     */
    removeExitHandlers(): void {
        if (this.exitHandlers.length > 0) {
            this.logger.info('Removing previously registered exit handlers');

            for (const { event, handler } of this.exitHandlers) {
                process.removeListener(event, handler);
            }

            this.exitHandlers = [];
        }
    }

    /**
     * Save parsed content to the database
     * @param parsedContentInfo Parsed content information
     * @returns ID of the saved parsed content
     */
    saveParsedContent(parsedContentInfo: ParsedContentInfo): number {
        return this.parsedContent.saveParsedContent(parsedContentInfo);
    }

    /**
     * Get parsed content by resource ID and content type
     * @param resourceId The ID of the resource
     * @param contentType Optional content type filter
     * @returns The parsed content or undefined if not found
     */
    getParsedContent(resourceId: number, contentType?: string): { id: number; resourceId: number; contentType: string; content: string } | undefined {
        return this.parsedContent.getParsedContentByResourceId(resourceId, contentType);
    }

    /**
     * Save conflicts to the database
     * @param conflicts Array of conflicts to save
     * @returns Number of conflicts saved
     */
    public saveConflicts(conflicts: ConflictInfo[]): number {
        return this.conflicts.saveConflicts(conflicts);
    }

    /**
     * Get conflicts from the database
     * @param limit Maximum number of conflicts to return
     * @param offset Offset for pagination
     * @returns Array of conflicts
     */
    public getConflicts(limit: number = 100, offset: number = 0): ConflictInfo[] {
        return this.conflicts.getConflicts(limit, offset);
    }

    /**
     * Save script analysis data to the database
     * @param resourceId Resource ID
     * @param analysisData Script analysis data
     * @returns ID of the saved script analysis data
     */
    public saveScriptAnalysisData(resourceId: number, analysisData: any): number {
        return this.scriptAnalysis.saveScriptAnalysisData(resourceId, analysisData);
    }

    /**
     * Get script analysis data from the database
     * @param resourceId Resource ID
     * @returns Script analysis data or null if not found
     */
    public getScriptAnalysisData(resourceId: number): any {
        const result = this.scriptAnalysis.getScriptAnalysisData(resourceId);
        return result ? result.analysisData : null;
    }

    /**
     * Save multiple resources to the database
     * @param resources Array of resource information
     * @returns Number of resources saved
     */
    public async saveResources(resources: any[]): Promise<number> {
        let savedCount = 0;

        for (const resource of resources) {
            try {
                this.resources.saveResource({
                    packageId: resource.packageId,
                    type: resource.type,
                    group: typeof resource.group === 'bigint' ? resource.group : BigInt(resource.group),
                    instance: typeof resource.instance === 'bigint' ? resource.instance : BigInt(resource.instance),
                    hash: resource.hash || '',
                    size: resource.size || 0,
                    offset: resource.offset || 0,
                    contentSnippet: resource.contentSnippet || '',
                    resourceType: resource.resourceType || 'Unknown'
                });
                savedCount++;
            } catch (error) {
                this.logger.error(`Error saving resource: ${error}`);
            }
        }

        return savedCount;
    }

    /**
     * Get all resources from the database
     * @param limit Maximum number of resources to return
     * @param offset Offset for pagination
     * @returns Array of resources with BigInt values for group and instance
     */
    public async getResources(limit: number = 1000, offset: number = 0): Promise<any[]> {
        try {
            const query = `
                SELECT r.*, p.name as packageName, p.path as packagePath
                FROM Resources r
                JOIN Packages p ON r.packageId = p.id
                LIMIT ? OFFSET ?
            `;

            // Use a read connection for this query
            const db = this.connectionPool.getReadConnection();
            const results = db.prepare(query).all(limit, offset);

            // Convert string group and instance values to BigInt
            return results.map((row: any) => ({
                ...row,
                group: typeof row.group === 'string' ? BigInt(row.group) : row.group,
                instance: typeof row.instance === 'string' ? BigInt(row.instance) : row.instance,
                // Add key property for conflict detection
                key: {
                    type: row.type,
                    group: typeof row.group === 'string' ? BigInt(row.group) : row.group,
                    instance: typeof row.instance === 'string' ? BigInt(row.instance) : row.instance
                },
                // Add metadata property for conflict detection
                metadata: {
                    name: row.resourceType || 'Unknown Resource',
                    path: row.packagePath || '',
                    hash: row.hash || '',
                    size: row.size || 0,
                    timestamp: Date.now()
                }
            }));
        } catch (error) {
            this.logger.error(`Error getting resources: ${error}`);
            return [];
        }
    }

    /**
     * Get a package by ID
     * @param packageId Package ID
     * @returns Package information or undefined if not found
     */
    public async getPackage(packageId: number): Promise<any | undefined> {
        try {
            const query = `
                SELECT * FROM Packages
                WHERE id = ?
            `;

            return this.db.prepare(query).get(packageId);
        } catch (error) {
            this.logger.error(`Error getting package ${packageId}: ${error}`);
            return undefined;
        }
    }

    /**
     * Delete script analysis data from the database
     * @param resourceId Resource ID
     * @returns True if deleted, false otherwise
     */
    public deleteScriptAnalysisData(resourceId: number): boolean {
        return this.scriptAnalysis.deleteScriptAnalysisData(resourceId);
    }

    /**
     * Get all metadata for a resource
     * @param resourceId Resource ID
     * @returns Array of metadata objects
     */
    public getMetadataByResourceId(resourceId: number): { id: number; resourceId: number; key: string; value: string }[] {
        return this.metadata.getMetadataByResourceId(resourceId);
    }

    /**
     * Get dependencies by source resource ID
     * @param sourceResourceId Source resource ID
     * @returns Array of dependencies
     */
    public getDependenciesBySourceId(sourceResourceId: number): DependencyInfo[] {
        return this.dependencies.getDependenciesBySourceId(sourceResourceId);
    }

    /**
     * Get dependencies by target TGI
     * @param targetType Target type
     * @param targetGroup Target group
     * @param targetInstance Target instance
     * @returns Array of dependencies
     */
    public getDependenciesByTargetTGI(
        targetType: number,
        targetGroup: bigint | string,
        targetInstance: bigint | string
    ): DependencyInfo[] {
        return this.dependencies.getDependenciesByTargetTGI(targetType, targetGroup, targetInstance);
    }

    /**
     * Get a resource by its ID
     * @param resourceId The ID of the resource to get
     * @returns The resource or undefined if not found
     */
    public getResourceById(resourceId: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string } | undefined {
        return this.resources.getResourceById(resourceId);
    }

    /**
     * Get the content-addressable storage instance
     * @returns The content-addressable storage instance
     */
    public getContentStorage(): ContentAddressableStorage {
        return this.contentStorage;
    }

    /**
     * Get the database path
     * @returns The database path
     */
    public getDatabasePath(): string {
        return this.dbPath;
    }

    /**
     * Get the write database connection
     * @returns The write database connection
     */
    public getDatabase(): Database.Database {
        return this.connectionPool.getWriteConnection();
    }

    /**
     * Get a read database connection
     * @returns A read database connection
     */
    public getReadConnection(): Database.Database {
        return this.connectionPool.getReadConnection();
    }

    /**
     * Reconnect to the database
     * This is used after a rollback operation
     */
    public reconnect(): void {
        // Close existing connection pool
        if (this.connectionPool) {
            try {
                this.connectionPool.close();
            } catch (error) {
                this.logger.warn('Error closing database connections during reconnect:', error);
            }
        }

        // Create a new connection
        if (this.dbPath === ':memory:') {
            this.connectionPool = new DatabaseConnectionPool(':memory:', this.logger);
        } else {
            this.connectionPool = new DatabaseConnectionPool(this.dbPath, this.logger);
        }

        this.logger.info('Database connections reconnected');
    }
}

// Export the class itself and types
export { DatabaseService };
export { DependencyInfo } from '../types/database.js';

// Optional: Add process exit handler to close DB connection (needs access to the instance)
// This will need to be handled by the code managing the DatabaseService instance.
// process.on('exit', () => {
//     databaseService.close(); // This line is now invalid as databaseService is not directly exported
// });
// process.on('SIGHUP', () => process.exit(128 + 1));
// process.on('SIGINT', () => process.exit(128 + 2));
// process.on('SIGTERM', () => process.exit(128 + 15));
