import { Logger } from '../../utils/logging/logger.js';
import { ResourceKey as AppResourceKey } from '../../types/resource/interfaces.js';
import { createResourceKeyFromS4TKWithName } from '../../utils/resource/helpers.js';
import { promises as fs } from 'fs';
import path from 'path';
// Import all from @s4tk/models and access components via the namespace
import * as S4TKModels from '@s4tk/models';
// Import the resource pool manager
import resourcePool from '../../utils/resource/resourcePoolManager.js';
// Import S4TK plugin manager
import { isBufferFromFilePluginAvailable } from '../../utils/s4tk/s4tkPluginManager.js';
// Import EventEmitter for setting max listeners
import { EventEmitter } from 'events';

const logger = new Logger('S4TKReader');

// Set max listeners for all file streams
// This is a workaround for the EventEmitter memory leak warnings
// The warnings occur because the S4TK library creates many file streams
// and attaches many listeners to them
function configureFileStreamMaxListeners() {
    // Set default max listeners to a higher value
    EventEmitter.defaultMaxListeners = 100;

    // Set max listeners for fs.ReadStream and fs.WriteStream
    if (fs.ReadStream && fs.ReadStream.prototype) {
        fs.ReadStream.prototype.setMaxListeners(100);
        logger.info('Increased max listeners for fs.ReadStream to 100');
    }

    if (fs.WriteStream && fs.WriteStream.prototype) {
        fs.WriteStream.prototype.setMaxListeners(100);
        logger.info('Increased max listeners for fs.WriteStream to 100');
    }

    // Set max listeners for process
    if (process && process.setMaxListeners) {
        process.setMaxListeners(100);
        logger.info('Increased max listeners for process to 100');
    }

    logger.info('File stream max listeners configured');
}

// Call the function to configure max listeners
configureFileStreamMaxListeners();

// Destructure necessary components from the imported namespace
const { Package, StringTableResource, RawResource, Resource: ResourceType, ResourceKey: S4TKResourceKey } = S4TKModels;

interface PackageFileReadingOptions {
    loadResources?: boolean; // Whether to load resource content (default: true)
}

// Define a type for the key-value pairs returned by the stream
interface LocalResourceKeyPair<T extends ResourceType> {
    key: S4TKResourceKey;
    value: T | null; // Value can be null if resource loading fails or is skipped
}


/**
 * Validates if a file is a Sims 4 package file using S4TK.
 * @param filePath Path to the file.
 * @returns True if valid, false otherwise.
 */
export async function validatePackageFile(filePath: string): Promise<boolean> {
    try {
        // Use the resource pool to read the file
        const buffer = await resourcePool.readFile(filePath);

        // Basic validation: Check magic number
        if (buffer.length < 4 || buffer.readUInt32LE(0) !== 0x46504244) { // 'DBPF'
            logger.warn(`File ${filePath} is not a valid DBPF file (magic number mismatch).`);
            return false;
        }

        // Attempt to load with S4TK for more thorough validation (optional, can be slow)
        // Package.extractResources(buffer, { loadResources: false }); // Throws if invalid structure

        logger.info(`File ${filePath} appears to be a valid package file.`);
        return true;
    } catch (error: any) {
        logger.warn(`S4TK validation failed for ${filePath}: ${error.message || error}`);
        return false;
    }
}

/**
 * Reads a Sims 4 package file and extracts only the resource keys.
 * @param filePath Path to the package file.
 * @returns Array of resource keys.
 */
export async function readPackageFileKeys(filePath: string): Promise<AppResourceKey[]> {
    try {
        // Use streamResourcePairs with loadResources: true to get key/value pairs
        // This ensures consistency with the main analysis flow and should capture all keys.
        const resourcePairs = await streamResourcePairs(filePath, { loadResources: true });
        const fileName = path.basename(filePath);

        const keys: AppResourceKey[] = resourcePairs.map(pair => {
            const rawKey = pair.key; // The key is directly available in the pair

            // --- DEBUG: Log raw key details before conversion ---
            if (rawKey.type === 0x7F4AD89D) { // Log only if it's the target type
                 logger.debug(`[readPackageFileKeys] Raw S4TK Key from stream (loadResources: true): Type=0x${rawKey.type.toString(16)}, Group=0x${rawKey.group.toString(16)}, Instance=0x${rawKey.instance.toString(16)}`);
            }
            // --- END DEBUG ---

            // Use helper to create AppResourceKey, passing file name and path
            return createResourceKeyFromS4TKWithName(rawKey, `Resource_${rawKey.instance}`, filePath);
        });

        logger.info(`Extracted ${keys.length} resource keys from ${filePath}.`);
        return keys;
    } catch (error: any) {
        logger.error(`Error reading package file keys from ${filePath}: ${error.message || error}`);
        throw new Error(`Failed to read keys from ${filePath}`);
    }
}


/**
 * Reads a Sims 4 package file and yields resource key/value pairs.
 * Uses streamResources when the BufferFromFile plugin is available, falling back to extractResources.
 * @param filePath Path to the package file.
 * @param options Optional settings for reading.
 * @returns Async generator yielding resource key/value pairs.
 */
export async function streamResourcePairs(filePath: string, options?: PackageFileReadingOptions): Promise<LocalResourceKeyPair<ResourceType>[]> {
    const loadResources = options?.loadResources ?? true; // Default to loading resources
    const resourcePairs: LocalResourceKeyPair<ResourceType>[] = [];

    try {
        // Check if the BufferFromFile plugin is available
        const bufferFromFileAvailable = isBufferFromFilePluginAvailable();

        if (bufferFromFileAvailable) {
            // Use the more efficient streamResources method when available
            logger.info(`Using Package.streamResourcesAsync for ${filePath} (more efficient)`);

            try {
                // Use the resource pool to limit concurrent operations
                const extractedEntries = await resourcePool.submit(`stream_${path.basename(filePath)}`, async () => {
                    return await Package.streamResourcesAsync(filePath, { loadResources });
                });

                logger.info(`Streamed ${extractedEntries.length} resources from ${filePath}`);

                // Process the entries
                for (const res of extractedEntries) {
                    const key = res.key;
                    const value = res.value;

                    if (!key) {
                        logger.warn(`Skipping entry in ${filePath} due to missing key.`);
                        continue;
                    }

                    resourcePairs.push({ key, value });
                }

                return resourcePairs;
            } catch (streamError: any) {
                logger.error(`Error using streamResourcesAsync for ${filePath}: ${streamError.message || streamError}`);
                logger.warn('Falling back to extractResources method...');
                // Fall back to the buffer-based approach
            }
        }

        // Fall back to the buffer-based approach if streamResources is not available or failed
        logger.info(`Reading file into buffer using resource pool: ${filePath}`);

        // Use the resource pool to read the file
        const buffer = await resourcePool.submit(`read_${path.basename(filePath)}`, async () => {
            return await fs.readFile(filePath);
        });

        logger.info(`File read into buffer (${buffer.length} bytes). Extracting resource pairs from buffer...`);

        let extractedEntries: any[];
        try {
            logger.info(`[S4TKReader TRY-NESTED] Calling Package.extractResources with buffer...`);

            // Use the resource pool to extract resources
            extractedEntries = await resourcePool.submit(`extract_${path.basename(filePath)}`, async () => {
                // Always load resources initially if loadResources is true, as S4TK might parse them correctly
                return Package.extractResources(buffer, { loadResources: loadResources });
            });

            logger.info(`[S4TKReader TRY-NESTED] Package.extractResources call completed.`);
        } catch (extractError: any) {
            logger.error(`[S4TKReader CATCH-NESTED] S4TK Package.extractResources failed for ${filePath}: ${extractError.message || extractError}`);
            throw new Error(`S4TK failed to extract resources from ${filePath}`);
        }

        logger.info(`Extracted ${extractedEntries.length} resources with content from ${filePath}`);

        // Process each entry, attempting re-parsing if necessary
        for (const res of extractedEntries) {
            let finalValue: ResourceType | null = res.value; // Start with the original value or null
            const originalRes = res.value; // Keep original for checks
            const key = res.key as S4TKResourceKey | undefined; // Get key early for logging

            if (!key) {
                logger.warn(`Skipping entry in ${filePath} due to missing key.`);
                continue; // Skip if key is missing
            }

            const keyString = key?.toString() ?? 'Unknown Key';

            // If resource loading was skipped or failed initially, finalValue might be null/undefined
            if (!loadResources || !originalRes) {
                 // If we need the resource but didn't load it, log a warning or handle appropriately
                 if (loadResources && !originalRes) {
                     logger.warn(`Resource ${keyString} was not loaded by S4TK extractResources.`);
                 }
                 // Add the pair with null value if resource wasn't loaded/needed
                 resourcePairs.push({ key, value: null });
                 continue; // Move to the next resource
            }


            // Check if the resource object seems valid and has the getBuffer method
            // This check is crucial for resources S4TK might partially parse but fail on buffer access
            // Check if the resource object seems valid but lacks getBuffer
            // The actual buffer extraction fallback is now handled in metadataExtractor
            if (typeof originalRes?.getBuffer !== 'function') {
                const type = key?.type;
                // Log that the buffer method is missing, but don't attempt re-parse here
                logger.warn(`S4TK object for ${keyString} (Type: ${type ? type.toString(16) : 'N/A'}) lacks getBuffer. Fallback will be attempted later.`);
                // Keep console.dir for detailed debugging if needed
                try {
                    console.dir(originalRes, { depth: null });
                } catch (e) {
                    console.warn('console.dir failed on resource object:', e);
                }
                // Keep the original (potentially incomplete) S4TK object
                finalValue = originalRes;
            }
            // Removed the block that attempted to find internal buffers and re-parse

            // Add the final key-value pair (original or re-parsed)
            resourcePairs.push({ key, value: finalValue });
        }

        return resourcePairs;

    } catch (error: any) {
        logger.error(`Error extracting resource pairs from package file ${filePath}: ${error.message || error}`);
        throw new Error(`Failed to stream resource pairs from ${filePath}`);
    }
}
