/**
 * Model Resource Transformer
 *
 * This module provides a specialized transformer for 3D model resources.
 * It processes model resources in a streaming fashion, extracting metadata,
 * vertices, faces, and other model components incrementally.
 */

import { TransformCallback } from 'stream';
import { Logger } from '../../../../utils/logging/logger.js';
import { BaseStreamTransformer, StreamTransformerOptions } from '../baseStreamTransformer.js';

// Create a logger for this module
const logger = new Logger('ModelResourceTransformer');

/**
 * Model parsing state
 */
enum ModelParsingState {
    HEADER = 'header',
    MODEL_INFO = 'model_info',
    MATERIALS = 'materials',
    VERTICES = 'vertices',
    FACES = 'faces',
    COMPLETE = 'complete'
}

/**
 * Model vertex
 */
interface ModelVertex {
    position: [number, number, number];
    normal?: [number, number, number];
    uv?: [number, number];
}

/**
 * Model face
 */
interface ModelFace {
    indices: [number, number, number];
    materialIndex?: number;
}

/**
 * Model material reference
 */
interface ModelMaterialRef {
    name: string;
    textureRefs: string[];
}

/**
 * Model data
 */
interface ModelData {
    name?: string;
    vertexCount: number;
    faceCount: number;
    materialCount: number;
    vertices: ModelVertex[];
    faces: ModelFace[];
    materials: ModelMaterialRef[];
}

/**
 * Model transformer options
 */
export interface ModelTransformerOptions extends StreamTransformerOptions {
    parseMaterials?: boolean;
    parseVertices?: boolean;
    parseFaces?: boolean;
    maxVertices?: number;
}

/**
 * Model resource transformer
 */
export class ModelResourceTransformer extends BaseStreamTransformer {
    private state: {
        parsingState: ModelParsingState;
        buffer: Buffer;
        bufferOffset: number;
        modelData: Partial<ModelData>;
        vertexParseCount: number;
        faceParseCount: number;
        materialParseCount: number;
    };

    private parseMaterials: boolean;
    private parseVertices: boolean;
    private parseFaces: boolean;
    private maxVertices: number;

    /**
     * Create a new model resource transformer
     * @param options Transformer options
     */
    constructor(options: ModelTransformerOptions = {}) {
        super('ModelResourceTransformer', options);

        this.parseMaterials = options.parseMaterials !== false;
        this.parseVertices = options.parseVertices !== false;
        this.parseFaces = options.parseFaces !== false;
        this.maxVertices = options.maxVertices || 100000; // 100k vertices max by default

        // Initialize state
        this.state = {
            parsingState: ModelParsingState.HEADER,
            buffer: Buffer.alloc(0),
            bufferOffset: 0,
            modelData: {
                vertices: [],
                faces: [],
                materials: [],
                vertexCount: 0,
                faceCount: 0,
                materialCount: 0
            },
            vertexParseCount: 0,
            faceParseCount: 0,
            materialParseCount: 0
        };

        this.logger.debug(`Created model transformer (parseMaterials: ${this.parseMaterials}, parseVertices: ${this.parseVertices})`);
    }

    /**
     * Reset transformer state
     */
    public reset(): void {
        super.reset();

        // Reset state
        this.state = {
            parsingState: ModelParsingState.HEADER,
            buffer: Buffer.alloc(0),
            bufferOffset: 0,
            modelData: {
                vertices: [],
                faces: [],
                materials: [],
                vertexCount: 0,
                faceCount: 0,
                materialCount: 0
            },
            vertexParseCount: 0,
            faceParseCount: 0,
            materialParseCount: 0
        };
    }

    /**
     * Get model data
     */
    public getModelData(): Partial<ModelData> {
        return { ...this.state.modelData };
    }

    /**
     * Process a chunk
     * @param chunk Chunk to process
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    protected processChunkImpl(
        chunk: Buffer,
        encoding: BufferEncoding,
        callback: (error?: Error | null, data?: any) => void
    ): void {
        try {
            // Append chunk to buffer
            this.appendToBuffer(chunk);

            // Process buffer based on current state
            this.processBuffer();

            // Pass the chunk through
            callback(null, chunk);

            // Emit data if we've completed parsing
            if (this.state.parsingState === ModelParsingState.COMPLETE) {
                this.emitModelData();
            }
        } catch (error: any) {
            this.logger.error(`Error processing chunk: ${error.message}`);
            callback(error);
        }
    }

    /**
     * Process buffer based on current parsing state
     */
    private processBuffer(): void {
        // Process buffer until we run out of data or complete parsing
        while (this.canContinueParsing()) {
            switch (this.state.parsingState) {
                case ModelParsingState.HEADER:
                    if (!this.parseHeader()) {
                        return; // Not enough data
                    }
                    break;

                case ModelParsingState.MODEL_INFO:
                    if (!this.parseModelInfo()) {
                        return; // Not enough data
                    }
                    break;

                case ModelParsingState.MATERIALS:
                    if (!this.parseMaterials || this.state.materialParseCount >= (this.state.modelData.materialCount || 0)) {
                        // Skip materials parsing or already parsed all materials
                        this.state.parsingState = ModelParsingState.VERTICES;
                        break;
                    }

                    if (!this.parseMaterialData()) {
                        return; // Not enough data
                    }
                    break;

                case ModelParsingState.VERTICES:
                    if (!this.parseVertices || this.state.vertexParseCount >= (this.state.modelData.vertexCount || 0)) {
                        // Skip vertices parsing or already parsed all vertices
                        this.state.parsingState = ModelParsingState.FACES;
                        break;
                    }

                    if (!this.parseVertexData()) {
                        return; // Not enough data
                    }
                    break;

                case ModelParsingState.FACES:
                    if (!this.parseFaces || this.state.faceParseCount >= (this.state.modelData.faceCount || 0)) {
                        // Skip faces parsing or already parsed all faces
                        this.state.parsingState = ModelParsingState.COMPLETE;
                        break;
                    }

                    if (!this.parseFaceData()) {
                        return; // Not enough data
                    }
                    break;

                case ModelParsingState.COMPLETE:
                    // All data parsed
                    return;
            }
        }
    }

    /**
     * Check if we can continue parsing
     */
    private canContinueParsing(): boolean {
        // Check if we have enough data to continue parsing
        return this.state.buffer.length - this.state.bufferOffset >= 4 &&
               this.state.parsingState !== ModelParsingState.COMPLETE;
    }

    /**
     * Append chunk to buffer
     * @param chunk Chunk to append
     */
    private appendToBuffer(chunk: Buffer): void {
        // Create a new buffer with the combined length
        const newBuffer = Buffer.alloc(this.state.buffer.length - this.state.bufferOffset + chunk.length);

        // Copy existing buffer (excluding processed data)
        this.state.buffer.copy(newBuffer, 0, this.state.bufferOffset);

        // Copy new chunk
        chunk.copy(newBuffer, this.state.buffer.length - this.state.bufferOffset);

        // Update buffer and reset offset
        this.state.buffer = newBuffer;
        this.state.bufferOffset = 0;
    }

    /**
     * Parse model header
     * @returns True if header was parsed successfully, false if more data is needed
     */
    private parseHeader(): boolean {
        // Need at least 12 bytes for header
        if (this.state.buffer.length - this.state.bufferOffset < 12) {
            return false;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Read header magic (4 bytes)
            const magicBytes = buffer.subarray(offset, offset + 4);
            const magic = magicBytes.toString('ascii');
            offset += 4;

            // Check magic
            if (magic !== 'MODL') {
                this.logger.warn(`Invalid model header magic: ${magic}. Skipping model parsing and passing through data.`);
                // Skip to complete state to pass through the data without parsing
                this.state.parsingState = ModelParsingState.COMPLETE;
                return true;
            }

            // Read version (4 bytes)
            const version = buffer.readUInt32LE(offset);
            offset += 4;

            // Read flags (4 bytes)
            const flags = buffer.readUInt32LE(offset);
            offset += 4;

            // Update state
            this.state.parsingState = ModelParsingState.MODEL_INFO;
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed model header: version=${version}, flags=${flags}`);

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing model header: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse model info
     * @returns True if model info was parsed successfully, false if more data is needed
     */
    private parseModelInfo(): boolean {
        // Need at least 16 bytes for model info
        if (this.state.buffer.length - this.state.bufferOffset < 16) {
            return false;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Read vertex count (4 bytes)
            const vertexCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Read face count (4 bytes)
            const faceCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Read material count (4 bytes)
            const materialCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Read name length (4 bytes)
            const nameLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the name
            if (buffer.length - offset < nameLength) {
                return false;
            }

            // Read name
            let name;
            if (nameLength > 0) {
                name = buffer.toString('utf8', offset, offset + nameLength);
                offset += nameLength;
            }

            // Update state
            this.state.modelData.vertexCount = vertexCount;
            this.state.modelData.faceCount = faceCount;
            this.state.modelData.materialCount = materialCount;
            this.state.modelData.name = name;

            this.state.parsingState = ModelParsingState.MATERIALS;
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed model info: name=${name}, vertexCount=${vertexCount}, faceCount=${faceCount}, materialCount=${materialCount}`);

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing model info: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse material data
     * @returns True if materials were parsed successfully, false if more data is needed
     */
    private parseMaterialData(): boolean {
        // Simplified implementation that parses one material at a time
        // In a real implementation, this would be more complex to handle
        // different material formats and properties

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Need at least 8 bytes for material header
            if (buffer.length - offset < 8) {
                return false;
            }

            // Read material name length (4 bytes)
            const nameLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Read texture count (4 bytes)
            const textureCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the name
            if (buffer.length - offset < nameLength) {
                return false;
            }

            // Read material name
            let name = '';
            if (nameLength > 0) {
                name = buffer.toString('utf8', offset, offset + nameLength);
                offset += nameLength;
            }

            // Read texture references
            const textureRefs: string[] = [];
            for (let i = 0; i < textureCount; i++) {
                // Need at least 4 bytes for texture ref length
                if (buffer.length - offset < 4) {
                    return false;
                }

                // Read texture ref length (4 bytes)
                const refLength = buffer.readUInt32LE(offset);
                offset += 4;

                // Check if we have enough data for the texture ref
                if (buffer.length - offset < refLength) {
                    return false;
                }

                // Read texture ref
                if (refLength > 0) {
                    const textureRef = buffer.toString('utf8', offset, offset + refLength);
                    textureRefs.push(textureRef);
                    offset += refLength;
                }
            }

            // Create material reference
            const material: ModelMaterialRef = {
                name: name || `Material${this.state.materialParseCount}`,
                textureRefs
            };

            // Add to materials array
            this.state.modelData.materials?.push(material);
            this.state.materialParseCount++;

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed material ${material.name} with ${textureRefs.length} textures`);

            // Check if we've parsed all materials
            if (this.state.materialParseCount >= (this.state.modelData.materialCount || 0)) {
                this.state.parsingState = ModelParsingState.VERTICES;
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing material: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse vertex data
     * @returns True if vertices were parsed successfully, false if more data is needed
     */
    private parseVertexData(): boolean {
        // Parse vertices in batches to avoid blocking for too long
        const batchSize = 1000;
        const remainingVertices = (this.state.modelData.vertexCount || 0) - this.state.vertexParseCount;
        const verticesToParse = Math.min(batchSize, remainingVertices, this.maxVertices - this.state.vertexParseCount);

        if (verticesToParse <= 0) {
            this.state.parsingState = ModelParsingState.FACES;
            return true;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Each vertex is 3 floats for position, 3 floats for normal, 2 floats for UV
            // 8 floats * 4 bytes = 32 bytes per vertex
            const bytesNeeded = verticesToParse * 32;

            if (buffer.length - offset < bytesNeeded) {
                return false; // Not enough data
            }

            for (let i = 0; i < verticesToParse; i++) {
                // Read position (3 floats = 12 bytes)
                const position: [number, number, number] = [
                    buffer.readFloatLE(offset),
                    buffer.readFloatLE(offset + 4),
                    buffer.readFloatLE(offset + 8)
                ];
                offset += 12;

                // Read normal (3 floats = 12 bytes)
                const normal: [number, number, number] = [
                    buffer.readFloatLE(offset),
                    buffer.readFloatLE(offset + 4),
                    buffer.readFloatLE(offset + 8)
                ];
                offset += 12;

                // Read UV (2 floats = 8 bytes)
                const uv: [number, number] = [
                    buffer.readFloatLE(offset),
                    buffer.readFloatLE(offset + 4)
                ];
                offset += 8;

                // Create vertex
                const vertex: ModelVertex = { position, normal, uv };

                // Add to vertices array
                this.state.modelData.vertices?.push(vertex);
                this.state.vertexParseCount++;
            }

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed ${verticesToParse} vertices (${this.state.vertexParseCount}/${this.state.modelData.vertexCount})`);

            // Check if we've parsed all vertices or reached max
            if (this.state.vertexParseCount >= (this.state.modelData.vertexCount || 0) ||
                this.state.vertexParseCount >= this.maxVertices) {
                this.state.parsingState = ModelParsingState.FACES;
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing vertices: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse face data
     * @returns True if faces were parsed successfully, false if more data is needed
     */
    private parseFaceData(): boolean {
        // Parse faces in batches to avoid blocking for too long
        const batchSize = 1000;
        const remainingFaces = (this.state.modelData.faceCount || 0) - this.state.faceParseCount;
        const facesToParse = Math.min(batchSize, remainingFaces);

        if (facesToParse <= 0) {
            this.state.parsingState = ModelParsingState.COMPLETE;
            return true;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Each face is 3 uint32s for indices, 1 uint32 for material index
            // 4 uint32s * 4 bytes = 16 bytes per face
            const bytesNeeded = facesToParse * 16;

            if (buffer.length - offset < bytesNeeded) {
                return false; // Not enough data
            }

            for (let i = 0; i < facesToParse; i++) {
                // Read indices (3 uint32s = 12 bytes)
                const indices: [number, number, number] = [
                    buffer.readUInt32LE(offset),
                    buffer.readUInt32LE(offset + 4),
                    buffer.readUInt32LE(offset + 8)
                ];
                offset += 12;

                // Read material index (1 uint32 = 4 bytes)
                const materialIndex = buffer.readUInt32LE(offset);
                offset += 4;

                // Create face
                const face: ModelFace = { indices, materialIndex };

                // Add to faces array
                this.state.modelData.faces?.push(face);
                this.state.faceParseCount++;
            }

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed ${facesToParse} faces (${this.state.faceParseCount}/${this.state.modelData.faceCount})`);

            // Check if we've parsed all faces
            if (this.state.faceParseCount >= (this.state.modelData.faceCount || 0)) {
                this.state.parsingState = ModelParsingState.COMPLETE;
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing faces: ${error.message}`);
            throw error;
        }
    }

    /**
     * Implementation-specific flush
     * @param callback Callback function
     */
    protected flushImpl(callback: TransformCallback): void {
        // If we haven't completed parsing, try to finalize
        if (this.state.parsingState !== ModelParsingState.COMPLETE) {
            this.logger.warn(`Flushing before parsing complete, current state: ${this.state.parsingState}`);
            this.emitModelData();
        }

        callback();
    }

    /**
     * Calculate progress (0-1)
     */
    protected calculateProgress(): number {
        switch (this.state.parsingState) {
            case ModelParsingState.HEADER:
                return 0.05;

            case ModelParsingState.MODEL_INFO:
                return 0.1;

            case ModelParsingState.MATERIALS:
                if (this.state.modelData.materialCount && this.state.modelData.materialCount > 0) {
                    return 0.1 + (0.1 * (this.state.materialParseCount / this.state.modelData.materialCount));
                }
                return 0.15;

            case ModelParsingState.VERTICES:
                if (this.state.modelData.vertexCount && this.state.modelData.vertexCount > 0) {
                    return 0.2 + (0.5 * (this.state.vertexParseCount / this.state.modelData.vertexCount));
                }
                return 0.45;

            case ModelParsingState.FACES:
                if (this.state.modelData.faceCount && this.state.modelData.faceCount > 0) {
                    return 0.7 + (0.3 * (this.state.faceParseCount / this.state.modelData.faceCount));
                }
                return 0.85;

            case ModelParsingState.COMPLETE:
                return 1.0;

            default:
                return 0;
        }
    }

    /**
     * Emit model data
     */
    private emitModelData(): void {
        // Emit model data event with current state
        this.emit('model_data', this.state.modelData);
    }
}