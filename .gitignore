# Dependencies
node_modules/

# Build output
dist/
build/
coverage/
*.js
*.js.map
*.d.ts
*.d.ts.map

# Source TypeScript files
src/**/*.js
src/**/*.js.map
src/**/*.d.ts
src/**/*.d.ts.map

# Allow JavaScript files in specific directories
!scripts/**/*.js
!config/**/*.js

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Logs and temporary files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
temp/
.cache/
temp-output.txt
comprehensive-workflow-output.txt

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Analysis and output directories
output/
analysis-output/

# Keep specific config files
!jest.config.js
!webpack.config.js

# Backup files
*.bak
*_backup.*
*_old.*

# ML model files and artifacts
models/**/*.bin
models/**/*.json
models/**/*.h5
models/**/*.onnx
models/**/*.pb
models/**/*.tflite
models/checkpoints/
models/saved_models/

# Analysis artifacts
analysis-output/
output/analysis/
output/reports/
output/logs/

# Documentation folder
docs/

# Virtual environment
venv/

# Database files
*.db
data/*.db
data/

# Temporary and cache files
**/.DS_Store
**/.Thumbs.db
**/.cache/
**/__pycache__/
**/*.pyc
**/.pytest_cache/
**/.coverage
coverage/

# Additional files and directories to ignore
.cursor/rules
.giga
.roo
.vscode
analysis-results
dist
firecrawl-mcp-server
handle
mcp-servers
temp
temp_mcp_servers
test
uploads
.augment-guidelines
.cursorrules
add-js-imports.mjs
analysis_report.md
casp_format_report.md
check_module.py
cline_mcp_settings.json
codebase-analysis.md
error_output.log
fix-imports.js
jest.config.ts
schema_relationships.dot
update-imports.ps1
