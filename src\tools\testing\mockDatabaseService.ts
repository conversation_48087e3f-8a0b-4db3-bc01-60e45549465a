/**
 * Mock Database Service
 * 
 * This module provides a mock database service for testing.
 * It implements the same interface as the real database service,
 * but stores data in memory instead of in a database.
 */

import { Logger } from '../../utils/logging/logger.js';

// Create a logger for this module
const logger = new Logger('MockDatabaseService');

/**
 * Mock database service
 */
export class DatabaseService {
    private initialized: boolean = false;
    private tables: Map<string, any[]> = new Map();
    private queries: string[] = [];
    
    /**
     * Initialize the database service
     */
    public async initialize(): Promise<void> {
        logger.info('Initializing mock database service');
        
        // Create tables
        this.tables.set('Packages', []);
        this.tables.set('Resources', []);
        this.tables.set('Metadata', []);
        this.tables.set('Conflicts', []);
        this.tables.set('ResourceRelationships', []);
        
        this.initialized = true;
        logger.info('Mock database service initialized');
    }
    
    /**
     * Execute a query
     * @param query Query to execute
     * @param params Query parameters
     * @returns Query results
     */
    public async executeQuery(query: string, params: any[] = []): Promise<any[]> {
        if (!this.initialized) {
            throw new Error('Database service not initialized');
        }
        
        // Store query for debugging
        this.queries.push(query);
        
        // Parse query to determine operation
        const operation = query.trim().split(' ')[0].toUpperCase();
        
        switch (operation) {
            case 'SELECT':
                return this.handleSelect(query, params);
            case 'INSERT':
                return this.handleInsert(query, params);
            case 'UPDATE':
                return this.handleUpdate(query, params);
            case 'DELETE':
                return this.handleDelete(query, params);
            default:
                logger.warn(`Unsupported operation: ${operation}`);
                return [];
        }
    }
    
    /**
     * Execute a batch of queries
     * @param queries Queries to execute
     * @returns Query results
     */
    public async executeBatch(queries: { query: string; params: any[] }[]): Promise<any[][]> {
        if (!this.initialized) {
            throw new Error('Database service not initialized');
        }
        
        const results: any[][] = [];
        
        for (const { query, params } of queries) {
            const result = await this.executeQuery(query, params);
            results.push(result);
        }
        
        return results;
    }
    
    /**
     * Close the database connection
     */
    public async close(): Promise<void> {
        logger.info('Closing mock database service');
        
        // Clear tables
        this.tables.clear();
        this.queries = [];
        
        this.initialized = false;
        logger.info('Mock database service closed');
    }
    
    /**
     * Handle SELECT query
     * @param query Query to execute
     * @param params Query parameters
     * @returns Query results
     * @private
     */
    private handleSelect(query: string, params: any[]): any[] {
        // Extract table name from query
        const tableMatch = query.match(/FROM\s+([a-zA-Z0-9_]+)/i);
        
        if (!tableMatch) {
            logger.warn(`Could not extract table name from query: ${query}`);
            return [];
        }
        
        const tableName = tableMatch[1];
        const table = this.tables.get(tableName);
        
        if (!table) {
            logger.warn(`Table not found: ${tableName}`);
            return [];
        }
        
        // Extract WHERE clause
        const whereMatch = query.match(/WHERE\s+(.*?)(?:ORDER BY|GROUP BY|LIMIT|$)/i);
        const whereClause = whereMatch ? whereMatch[1] : null;
        
        // Extract LIMIT clause
        const limitMatch = query.match(/LIMIT\s+(\d+)(?:\s+OFFSET\s+(\d+))?/i);
        const limit = limitMatch ? parseInt(limitMatch[1], 10) : null;
        const offset = limitMatch && limitMatch[2] ? parseInt(limitMatch[2], 10) : 0;
        
        // Filter results based on WHERE clause
        let results = table;
        
        if (whereClause) {
            // Very simple WHERE clause parsing for testing
            // This only handles basic conditions like "id = ?" or "packageId IN (1,2,3)"
            results = table.filter(row => {
                // Handle packageId IN (...)
                if (whereClause.includes('packageId IN')) {
                    const packageIds = params[0]; // Assuming the first parameter is the array of package IDs
                    return packageIds.includes(row.packageId);
                }
                
                // Handle id = ?
                if (whereClause.includes('id = ?')) {
                    return row.id === params[0];
                }
                
                // Handle path = ?
                if (whereClause.includes('path = ?')) {
                    return row.path === params[0];
                }
                
                // Default: include all rows
                return true;
            });
        }
        
        // Apply LIMIT and OFFSET
        if (limit !== null) {
            results = results.slice(offset, offset + limit);
        }
        
        return results;
    }
    
    /**
     * Handle INSERT query
     * @param query Query to execute
     * @param params Query parameters
     * @returns Query results
     * @private
     */
    private handleInsert(query: string, params: any[]): any[] {
        // Extract table name from query
        const tableMatch = query.match(/INTO\s+([a-zA-Z0-9_]+)/i);
        
        if (!tableMatch) {
            logger.warn(`Could not extract table name from query: ${query}`);
            return [];
        }
        
        const tableName = tableMatch[1];
        const table = this.tables.get(tableName);
        
        if (!table) {
            logger.warn(`Table not found: ${tableName}`);
            return [];
        }
        
        // Extract column names
        const columnsMatch = query.match(/\(([^)]+)\)/);
        
        if (!columnsMatch) {
            logger.warn(`Could not extract column names from query: ${query}`);
            return [];
        }
        
        const columns = columnsMatch[1].split(',').map(col => col.trim());
        
        // Create row object
        const row: any = {};
        
        // Generate ID if needed
        if (columns.includes('id')) {
            row.id = table.length + 1;
        }
        
        // Set values from parameters
        for (let i = 0; i < columns.length; i++) {
            if (columns[i] !== 'id') {
                row[columns[i]] = params[i];
            }
        }
        
        // Add row to table
        table.push(row);
        
        // Return inserted row
        return [row];
    }
    
    /**
     * Handle UPDATE query
     * @param query Query to execute
     * @param params Query parameters
     * @returns Query results
     * @private
     */
    private handleUpdate(query: string, params: any[]): any[] {
        // Extract table name from query
        const tableMatch = query.match(/UPDATE\s+([a-zA-Z0-9_]+)/i);
        
        if (!tableMatch) {
            logger.warn(`Could not extract table name from query: ${query}`);
            return [];
        }
        
        const tableName = tableMatch[1];
        const table = this.tables.get(tableName);
        
        if (!table) {
            logger.warn(`Table not found: ${tableName}`);
            return [];
        }
        
        // Extract SET clause
        const setMatch = query.match(/SET\s+(.*?)(?:WHERE|$)/i);
        
        if (!setMatch) {
            logger.warn(`Could not extract SET clause from query: ${query}`);
            return [];
        }
        
        const setClauses = setMatch[1].split(',').map(clause => clause.trim());
        
        // Extract WHERE clause
        const whereMatch = query.match(/WHERE\s+(.*?)$/i);
        const whereClause = whereMatch ? whereMatch[1] : null;
        
        // Update rows
        const updatedRows: any[] = [];
        
        for (let i = 0; i < table.length; i++) {
            const row = table[i];
            
            // Check WHERE clause
            if (whereClause) {
                // Very simple WHERE clause parsing for testing
                if (whereClause.includes('id = ?')) {
                    if (row.id !== params[0]) {
                        continue;
                    }
                }
            }
            
            // Update row
            for (let j = 0; j < setClauses.length; j++) {
                const clause = setClauses[j];
                const parts = clause.split('=').map(part => part.trim());
                
                if (parts.length === 2) {
                    const column = parts[0];
                    
                    // If the value is a parameter placeholder
                    if (parts[1] === '?') {
                        row[column] = params[j];
                    } else {
                        // Otherwise, parse the value
                        row[column] = parts[1];
                    }
                }
            }
            
            updatedRows.push(row);
        }
        
        return updatedRows;
    }
    
    /**
     * Handle DELETE query
     * @param query Query to execute
     * @param params Query parameters
     * @returns Query results
     * @private
     */
    private handleDelete(query: string, params: any[]): any[] {
        // Extract table name from query
        const tableMatch = query.match(/FROM\s+([a-zA-Z0-9_]+)/i);
        
        if (!tableMatch) {
            logger.warn(`Could not extract table name from query: ${query}`);
            return [];
        }
        
        const tableName = tableMatch[1];
        const table = this.tables.get(tableName);
        
        if (!table) {
            logger.warn(`Table not found: ${tableName}`);
            return [];
        }
        
        // Extract WHERE clause
        const whereMatch = query.match(/WHERE\s+(.*?)$/i);
        const whereClause = whereMatch ? whereMatch[1] : null;
        
        // Delete rows
        const deletedRows: any[] = [];
        
        if (whereClause) {
            // Very simple WHERE clause parsing for testing
            if (whereClause.includes('id = ?')) {
                const id = params[0];
                
                for (let i = table.length - 1; i >= 0; i--) {
                    if (table[i].id === id) {
                        deletedRows.push(table.splice(i, 1)[0]);
                    }
                }
            }
        } else {
            // Delete all rows
            deletedRows.push(...table);
            table.length = 0;
        }
        
        return deletedRows;
    }
}
