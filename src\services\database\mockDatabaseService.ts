/**
 * Mock Database Service
 * 
 * This module provides a mock implementation of the database service for testing.
 */

import { DatabaseService } from './databaseService.js';
import { createPrintFunction } from '../../utils/console/consoleOutput.js';

// Create a print function for direct output
const print = createPrintFunction();

/**
 * Mock implementation of the database service for testing
 */
export class MockDatabaseService implements DatabaseService {
    private initialized: boolean = false;
    private closed: boolean = false;
    private data: Map<string, any> = new Map();
    
    /**
     * Initialize the database service
     */
    async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }
        
        print('Initializing mock database service...');
        this.initialized = true;
    }
    
    /**
     * Close the database service
     */
    async close(): Promise<void> {
        if (!this.initialized || this.closed) {
            return;
        }
        
        print('Closing mock database service...');
        this.closed = true;
    }
    
    /**
     * Insert data into the database
     * @param table Table name
     * @param data Data to insert
     */
    async insert(table: string, data: any): Promise<number> {
        this.ensureInitialized();
        
        const id = Date.now() + Math.floor(Math.random() * 1000);
        const key = `${table}:${id}`;
        
        this.data.set(key, { ...data, id });
        
        return id;
    }
    
    /**
     * Insert multiple records into the database
     * @param table Table name
     * @param records Records to insert
     */
    async insertMany(table: string, records: any[]): Promise<number[]> {
        this.ensureInitialized();
        
        const ids: number[] = [];
        
        for (const record of records) {
            const id = await this.insert(table, record);
            ids.push(id);
        }
        
        return ids;
    }
    
    /**
     * Update data in the database
     * @param table Table name
     * @param id Record ID
     * @param data Data to update
     */
    async update(table: string, id: number, data: any): Promise<void> {
        this.ensureInitialized();
        
        const key = `${table}:${id}`;
        
        if (!this.data.has(key)) {
            throw new Error(`Record not found: ${key}`);
        }
        
        const existingData = this.data.get(key);
        this.data.set(key, { ...existingData, ...data });
    }
    
    /**
     * Delete data from the database
     * @param table Table name
     * @param id Record ID
     */
    async delete(table: string, id: number): Promise<void> {
        this.ensureInitialized();
        
        const key = `${table}:${id}`;
        
        if (!this.data.has(key)) {
            throw new Error(`Record not found: ${key}`);
        }
        
        this.data.delete(key);
    }
    
    /**
     * Get data from the database
     * @param table Table name
     * @param id Record ID
     */
    async get(table: string, id: number): Promise<any> {
        this.ensureInitialized();
        
        const key = `${table}:${id}`;
        
        if (!this.data.has(key)) {
            return null;
        }
        
        return this.data.get(key);
    }
    
    /**
     * Query data from the database
     * @param table Table name
     * @param query Query object
     */
    async query(table: string, query: any): Promise<any[]> {
        this.ensureInitialized();
        
        const results: any[] = [];
        
        for (const [key, value] of this.data.entries()) {
            if (!key.startsWith(`${table}:`)) {
                continue;
            }
            
            let match = true;
            
            for (const [queryKey, queryValue] of Object.entries(query)) {
                if (value[queryKey] !== queryValue) {
                    match = false;
                    break;
                }
            }
            
            if (match) {
                results.push(value);
            }
        }
        
        return results;
    }
    
    /**
     * Execute a raw SQL query
     * @param sql SQL query
     * @param params Query parameters
     */
    async execute(sql: string, params: any[] = []): Promise<any> {
        this.ensureInitialized();
        
        // Mock implementation - just log the query
        print(`Executing SQL query: ${sql}`);
        print(`Parameters: ${JSON.stringify(params)}`);
        
        return {
            rows: [],
            rowCount: 0
        };
    }
    
    /**
     * Begin a transaction
     */
    async beginTransaction(): Promise<void> {
        this.ensureInitialized();
        print('Beginning transaction...');
    }
    
    /**
     * Commit a transaction
     */
    async commitTransaction(): Promise<void> {
        this.ensureInitialized();
        print('Committing transaction...');
    }
    
    /**
     * Rollback a transaction
     */
    async rollbackTransaction(): Promise<void> {
        this.ensureInitialized();
        print('Rolling back transaction...');
    }
    
    /**
     * Ensure the database service is initialized
     * @private
     */
    private ensureInitialized(): void {
        if (!this.initialized) {
            throw new Error('Database service not initialized');
        }
        
        if (this.closed) {
            throw new Error('Database service already closed');
        }
    }
}
