/**
 * Comprehensive System Tests
 * 
 * This module provides comprehensive testing for all core system components:
 * - PackageAnalyzer with all extractors
 * - DatabaseService CRUD operations
 * - ResourceTracker concurrent operations
 * - EnhancedMemoryManager stress testing
 * - ConflictDetector all 4 detectors
 * - Large-scale scenarios (10,000+ resources)
 * - Error handling and recovery
 */

import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { createPackageAnalyzer } from '../../services/analysis/packageAnalyzerFactory.js';
import { ConflictDetector } from '../../services/conflict/ConflictDetector.js';
import { EnhancedMemoryManager } from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { Logger } from '../../utils/logging/logger.js';
import { findPackageFiles } from './fileScanner.js';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Comprehensive test result interface
 */
export interface ComprehensiveTestResult {
    success: boolean;
    testName: string;
    duration: number;
    details: {
        packageAnalyzer?: any;
        databaseService?: any;
        resourceTracker?: any;
        memoryManager?: any;
        conflictDetector?: any;
        extractors?: any;
        largeScale?: any;
        errorHandling?: any;
        performance?: any;
    };
    errors: string[];
    warnings: string[];
    metrics: {
        memoryUsage: any;
        processingTime: number;
        resourcesProcessed: number;
        conflictsDetected: number;
    };
}

/**
 * Test options interface
 */
export interface ComprehensiveTestOptions {
    maxPackages?: number;
    logLevel?: string;
    useInMemoryDatabase?: boolean;
    testMode?: 'minimal' | 'standard' | 'comprehensive' | 'stress';
    enableMemoryProfiling?: boolean;
    enablePerformanceProfiling?: boolean;
    testConcurrency?: boolean;
    testErrorRecovery?: boolean;
}

/**
 * Test PackageAnalyzer with all extractors
 */
export async function testPackageAnalyzerComprehensive(
    modsPath: string,
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};
    let metrics: any = {};

    const logger = new Logger('ComprehensivePackageAnalyzerTest');
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== COMPREHENSIVE PACKAGE ANALYZER TEST =====');

        // Initialize database
        const databaseService = new DatabaseService(options.useInMemoryDatabase ? ':memory:' : 'test_comprehensive.db');
        await databaseService.initialize();

        // Track database for cleanup
        const testId = Date.now();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `packageAnalyzerTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_${testId}`, state: ResourceState.ACTIVE }
        );

        // Create package analyzer
        const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
        await packageAnalyzer.initialize();

        // Test with real package files
        const packageFiles = await findPackageFiles(modsPath, {
            maxFiles: options.maxPackages || 50,
            maxDepth: 3,
            randomize: true
        });

        logger.info(`Found ${packageFiles.length} package files for testing`);

        if (packageFiles.length === 0) {
            warnings.push('No package files found for testing');
            return {
                success: false,
                testName: 'Comprehensive Package Analyzer Test',
                duration: Date.now() - startTime,
                details,
                errors: ['No package files found'],
                warnings,
                metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
            };
        }

        // Test different batch sizes
        const batchSizes = [1, 5, 10, 25];
        const extractorResults: any = {};

        for (const batchSize of batchSizes) {
            if (packageFiles.length < batchSize) continue;

            logger.info(`Testing batch size: ${batchSize}`);
            const batchStartTime = Date.now();
            const batch = packageFiles.slice(0, batchSize);

            let resourcesProcessed = 0;
            const extractorCounts: Record<string, number> = {};

            for (const packageFile of batch) {
                try {
                    logger.debug(`Processing: ${path.basename(packageFile)}`);
                    
                    const result = await packageAnalyzer.analyzePackage(packageFile);
                    
                    if (result.success && result.resources) {
                        resourcesProcessed += result.resources.length;
                        
                        // Count extractor usage
                        for (const resource of result.resources) {
                            const extractorType = resource.resourceType || 'UNKNOWN';
                            extractorCounts[extractorType] = (extractorCounts[extractorType] || 0) + 1;
                        }
                    }

                } catch (error: any) {
                    errors.push(`Error processing ${packageFile}: ${error.message}`);
                }
            }

            extractorResults[`batch_${batchSize}`] = {
                duration: Date.now() - batchStartTime,
                resourcesProcessed,
                extractorCounts,
                averageTimePerResource: resourcesProcessed > 0 ? (Date.now() - batchStartTime) / resourcesProcessed : 0
            };

            logger.info(`Batch ${batchSize}: ${resourcesProcessed} resources processed in ${Date.now() - batchStartTime}ms`);
        }

        details.packageAnalyzer = {
            totalPackagesAvailable: packageFiles.length,
            batchResults: extractorResults,
            memoryUsage: memoryManager.getMemoryStats()
        };

        metrics = {
            memoryUsage: memoryManager.getMemoryStats(),
            processingTime: Date.now() - startTime,
            resourcesProcessed: Object.values(extractorResults).reduce((sum: number, batch: any) => sum + batch.resourcesProcessed, 0),
            conflictsDetected: 0
        };

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`packageAnalyzerTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Comprehensive Package Analyzer Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Comprehensive Package Analyzer Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}

/**
 * Test DatabaseService CRUD operations comprehensively
 */
export async function testDatabaseServiceComprehensive(
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};

    const logger = new Logger('ComprehensiveDatabaseTest');
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== COMPREHENSIVE DATABASE SERVICE TEST =====');

        // Initialize database
        const databaseService = new DatabaseService(options.useInMemoryDatabase ? ':memory:' : 'test_db_comprehensive.db');
        await databaseService.initialize();

        const testId = Date.now();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `databaseTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_test_${testId}`, state: ResourceState.ACTIVE }
        );

        // Test resource operations
        logger.info('Testing resource CRUD operations...');
        const resourceTests = await testResourceCRUD(databaseService);
        
        // Test metadata operations
        logger.info('Testing metadata CRUD operations...');
        const metadataTests = await testMetadataCRUD(databaseService);
        
        // Test dependency operations
        logger.info('Testing dependency CRUD operations...');
        const dependencyTests = await testDependencyCRUD(databaseService);
        
        // Test conflict operations
        logger.info('Testing conflict CRUD operations...');
        const conflictTests = await testConflictCRUD(databaseService);

        details.databaseService = {
            resourceTests,
            metadataTests,
            dependencyTests,
            conflictTests
        };

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`databaseTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Comprehensive Database Service Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: Date.now() - startTime, resourcesProcessed: 0, conflictsDetected: 0 }
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Comprehensive Database Service Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}

// Helper functions for database testing
async function testResourceCRUD(databaseService: DatabaseService): Promise<any> {
    // Implementation will be added in next edit
    return { placeholder: 'Resource CRUD tests' };
}

async function testMetadataCRUD(databaseService: DatabaseService): Promise<any> {
    // Implementation will be added in next edit
    return { placeholder: 'Metadata CRUD tests' };
}

async function testDependencyCRUD(databaseService: DatabaseService): Promise<any> {
    // Implementation will be added in next edit
    return { placeholder: 'Dependency CRUD tests' };
}

async function testConflictCRUD(databaseService: DatabaseService): Promise<any> {
    // Implementation will be added in next edit
    return { placeholder: 'Conflict CRUD tests' };
}
