/**
 * Enhanced Content-Addressable Storage
 *
 * This module provides a memory-efficient content-addressable storage system for large content snippets.
 * It uses file-based storage with content hashing for deduplication, directory sharding,
 * reference counting, and memory caching with LRU eviction policy.
 *
 * Key features:
 * - Content fingerprinting for efficient deduplication
 * - Explicit reference counting for content lifecycle management
 * - Memory-aware storage policies with dynamic cache management
 * - Content eviction based on reference count and memory pressure
 * - Streaming content access for large files
 */

import path from 'path';
import { createHash } from 'crypto';
import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { LRUCache } from 'lru-cache';
import { promises as fsPromises } from 'fs';
import { createReadStream } from 'fs';
import { pipeline } from 'stream/promises';
import { Readable } from 'stream';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { EnhancedBufferPool } from '../../utils/memory/enhancedBufferPool.js';
import { simhash, hammingDistance, similarityPercentage } from '../../utils/hashing/simhash.js';

// Create a logger for this module
const logger = new Logger('ContentAddressableStorage');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Content storage options
 */
export interface ContentStorageOptions {
    basePath?: string;
    shardingLevels?: number;
    shardingWidth?: number;
    maxCacheSize?: number;
    maxCacheAge?: number;
    bufferPoolSize?: number;
    maxBufferSize?: number;
    streamingThreshold?: number;
    fingerprintingEnabled?: boolean;
}

/**
 * Content metadata
 */
export interface ContentMetadata {
    contentHash: string;
    contentPath: string;
    contentType: string;
    size: number;
    referenceCount: number;
    timestamp: number;
    fingerprint?: string;
    lastAccessTime?: number;
    accessCount?: number;
}

/**
 * Content-Addressable Storage class
 * Implements the Singleton pattern to ensure only one storage instance exists
 */
export class ContentAddressableStorage {
    private static instance: ContentAddressableStorage;
    private databaseService: DatabaseService | null = null;
    private basePath: string;
    private shardingLevels: number;
    private shardingWidth: number;
    private contentCache: LRUCache<string, Buffer>;
    private bufferPool: EnhancedBufferPool;
    private streamingThreshold: number;
    private fingerprintingEnabled: boolean;
    private initialized: boolean = false;

    /**
     * Private constructor to enforce Singleton pattern
     * @param databaseService Optional database service instance
     * @param options Content storage options
     */
    private constructor(databaseService?: DatabaseService, options: ContentStorageOptions = {}) {
        this.databaseService = databaseService || null;
        this.basePath = options.basePath || path.join(process.cwd(), 'data', 'content');
        this.shardingLevels = options.shardingLevels || 2;
        this.shardingWidth = options.shardingWidth || 2;
        this.streamingThreshold = options.streamingThreshold || 10 * 1024 * 1024; // 10MB default
        this.fingerprintingEnabled = options.fingerprintingEnabled ?? true; // Enabled by default

        // Initialize buffer pool for streaming operations
        this.bufferPool = EnhancedBufferPool.getInstance({
            maxBuffers: options.bufferPoolSize || 5,
            maxMemory: (options.bufferPoolSize || 5) * (options.maxBufferSize || 1024 * 1024) // Total memory based on pool size and buffer size
        });

        // Initialize content cache with smaller size for better memory efficiency
        this.contentCache = new LRUCache<string, Buffer>({
            max: options.maxCacheSize || 50 * 1024 * 1024, // 50 MB (reduced from 100 MB)
            ttl: options.maxCacheAge || 30 * 60 * 1000, // 30 minutes (reduced from 1 hour)
            maxSize: options.maxCacheSize || 50 * 1024 * 1024, // 50 MB
            sizeCalculation: (value: Buffer, _key: string) => {
                return value.length;
            },
            dispose: (value: Buffer, _key: string) => {
                // Help garbage collection by nullifying the buffer
                if (value) {
                    // Clear buffer reference to help garbage collection
                    for (let i = 0; i < value.length; i++) {
                        value[i] = 0;
                    }
                }
            }
        });

        logger.info('Content-Addressable Storage created');
    }

    /**
     * Set the database service instance
     * @param databaseService The database service instance
     */
    public setDatabaseService(databaseService: DatabaseService): void {
        this.databaseService = databaseService;
        logger.info('Database service set for Content-Addressable Storage');
    }

    /**
     * Get the ContentAddressableStorage instance (Singleton pattern)
     * @param databaseService Optional database service instance
     * @param options Content storage options (only used on first call)
     * @returns The ContentAddressableStorage instance
     */
    public static getInstance(databaseService?: DatabaseService, options?: ContentStorageOptions): ContentAddressableStorage {
        if (!ContentAddressableStorage.instance) {
            ContentAddressableStorage.instance = new ContentAddressableStorage(databaseService, options);
        } else if (databaseService && !ContentAddressableStorage.instance.databaseService) {
            // If instance exists but doesn't have a database service, set it
            ContentAddressableStorage.instance.setDatabaseService(databaseService);
        }
        return ContentAddressableStorage.instance;
    }

    /**
     * Initialize the content storage (lazy initialization)
     * @returns Promise resolving when initialization is complete
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            logger.info(`Lazy initializing content storage at ${this.basePath}`);

            // Only create base directory, defer database operations until needed
            await fsPromises.mkdir(this.basePath, { recursive: true });

            this.initialized = true;
            logger.info('Content storage lazy initialization completed');
        } catch (error: any) {
            logger.error(`Content storage initialization failed: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Initialize database tables (called only when needed)
     * @private
     */
    private async initializeDatabase(): Promise<void> {
        if (!this.databaseService) {
            return;
        }

        try {
            // Create content store table with all columns at once (more efficient)
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS ContentStore (
                    contentHash TEXT PRIMARY KEY,
                    contentPath TEXT NOT NULL,
                    contentType TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    referenceCount INTEGER NOT NULL DEFAULT 1,
                    timestamp INTEGER NOT NULL,
                    fingerprint TEXT,
                    lastAccessTime INTEGER,
                    accessCount INTEGER DEFAULT 0
                )
            `);

            // Create indices in a single transaction
            await this.databaseService.run(`
                CREATE INDEX IF NOT EXISTS idx_content_hash ON ContentStore (contentHash);
                CREATE INDEX IF NOT EXISTS idx_fingerprint ON ContentStore (fingerprint);
            `);

            logger.debug('Content storage database tables initialized');
        } catch (error: any) {
            logger.warn(`Error initializing content storage database: ${error.message || error}`);
            // Continue without database support
        }
    }

    /**
     * Store content in the content-addressable storage
     * @param content Content to store (string or Buffer)
     * @param contentType Content type (e.g., 'text/xml', 'application/json')
     * @returns Promise resolving to the content metadata
     */
    public async storeContent(
        content: string | Buffer,
        contentType: string
    ): Promise<ContentMetadata> {
        if (!this.initialized) {
            await this.initialize();
        }

        // Initialize database tables if needed (lazy)
        await this.initializeDatabase();

        // Check memory pressure before storing content
        this.checkMemoryPressure();

        try {
            // Convert string to Buffer if needed
            const contentBuffer = typeof content === 'string' ? Buffer.from(content) : content;

            // Calculate content hash
            const contentHash = createHash('sha256').update(contentBuffer).digest('hex');

            // Generate content fingerprint if enabled
            let fingerprint: string | undefined;
            if (this.fingerprintingEnabled) {
                fingerprint = this.generateFingerprint(contentBuffer).toString();
            }

            // Check if content already exists
            const existingContent = await this.getContentMetadata(contentHash);
            if (existingContent) {
                // Increment reference count
                await this.incrementReferenceCount(contentHash);

                // Update cache only if memory pressure is not too high
                const memoryPressure = memoryManager.getMemoryPressure();

                if (memoryPressure < 0.8) {
                    this.contentCache.set(contentHash, contentBuffer);
                    logger.debug(`Updated cache for existing content with hash ${contentHash}`);
                }

                logger.debug(`Content with hash ${contentHash} already exists, incremented reference count`);
                return existingContent;
            }

            // Check for similar content if fingerprinting is enabled
            if (this.fingerprintingEnabled && fingerprint) {
                const similarContent = await this.findSimilarContent(fingerprint);
                if (similarContent) {
                    logger.info(`Found similar content with hash ${similarContent.contentHash}, similarity: ${similarContent.similarity}%`);
                }
            }

            // Generate content path
            const contentPath = this.generateContentPath(contentHash);
            const fullPath = path.join(this.basePath, contentPath);

            // Create directory if it doesn't exist
            await fsPromises.mkdir(path.dirname(fullPath), { recursive: true });

            // Write content to file
            await fsPromises.writeFile(fullPath, contentBuffer);

            // Store content metadata in database
            const metadata: ContentMetadata = {
                contentHash,
                contentPath,
                contentType,
                size: contentBuffer.length,
                referenceCount: 1,
                timestamp: Date.now(),
                fingerprint,
                lastAccessTime: Date.now(),
                accessCount: 0
            };

            await this.storeContentMetadata(metadata);

            // Update cache only if memory pressure is not too high
            const memoryPressure = memoryManager.getMemoryPressure();

            if (memoryPressure < 0.8) {
                this.contentCache.set(contentHash, contentBuffer);
                logger.debug(`Added new content with hash ${contentHash} to cache`);
            } else {
                logger.debug(`Skipped caching new content with hash ${contentHash} due to high memory pressure (${(memoryPressure * 100).toFixed(1)}%)`);
            }

            logger.debug(`Stored content with hash ${contentHash}, size: ${contentBuffer.length} bytes`);

            // Help garbage collection
            if (typeof content === 'string') {
                // Clear reference to original string
                content = '';
            }

            return metadata;
        } catch (error: any) {
            logger.error(`Error storing content: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Generate a fingerprint for content
     * @param content Content to fingerprint
     * @returns Fingerprint as a BigInt
     * @private
     */
    private generateFingerprint(content: Buffer | string): bigint {
        try {
            return simhash(content);
        } catch (error: any) {
            logger.error(`Error generating fingerprint: ${error.message || error}`);
            // Return a default fingerprint
            return BigInt(0);
        }
    }

    /**
     * Find similar content based on fingerprint
     * @param fingerprint Content fingerprint
     * @param similarityThreshold Similarity threshold (default: 80)
     * @returns Similar content metadata and similarity percentage, or null if not found
     * @private
     */
    private async findSimilarContent(fingerprint: string): Promise<{ contentHash: string, similarity: number } | null> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                return null;
            }

            // Get all content with fingerprints
            const result = await this.databaseService.executeQuery(`
                SELECT contentHash, fingerprint
                FROM ContentStore
                WHERE fingerprint IS NOT NULL
            `);

            if (!result || result.length === 0) {
                return null;
            }

            // Convert fingerprint to BigInt
            const targetFingerprint = BigInt(fingerprint);

            // Find the most similar content
            let mostSimilarContent: { contentHash: string, similarity: number } | null = null;

            for (const content of result) {
                if (!content.fingerprint) continue;

                // Convert fingerprint to BigInt
                const contentFingerprint = BigInt(content.fingerprint);

                // Calculate similarity percentage
                const similarity = similarityPercentage(targetFingerprint, contentFingerprint);

                // Update most similar content if this one is more similar
                if (similarity >= 80 && (!mostSimilarContent || similarity > mostSimilarContent.similarity)) {
                    mostSimilarContent = {
                        contentHash: content.contentHash,
                        similarity
                    };
                }
            }

            return mostSimilarContent;
        } catch (error: any) {
            logger.error(`Error finding similar content: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Get content from the content-addressable storage
     * @param contentHash Content hash
     * @returns Promise resolving to the content buffer, or null if not found
     */
    public async getContent(contentHash: string): Promise<Buffer | null> {
        if (!this.initialized) {
            await this.initialize();
        }

        // Check memory pressure before accessing cache
        this.checkMemoryPressure();

        try {
            // Get content metadata
            const metadata = await this.getContentMetadata(contentHash);
            if (!metadata) {
                logger.warn(`Content with hash ${contentHash} not found`);
                return null;
            }

            // Update access statistics
            await this.updateAccessStats(contentHash);

            // For large content, use streaming approach
            if (metadata.size > this.streamingThreshold) {
                logger.debug(`Content size (${metadata.size} bytes) exceeds streaming threshold (${this.streamingThreshold} bytes), using streaming approach`);
                return this.getContentStreaming(contentHash, metadata);
            }

            // For smaller content, check cache first
            const cachedContent = this.contentCache.get(contentHash);
            if (cachedContent) {
                logger.debug(`Retrieved content with hash ${contentHash} from cache`);
                return cachedContent;
            }

            // Read content from file
            const fullPath = path.join(this.basePath, metadata.contentPath);
            const content = await fsPromises.readFile(fullPath);

            // Update cache only if memory pressure is not too high
            const memoryPressure = memoryManager.getMemoryPressure();

            if (memoryPressure < 0.8) {
                this.contentCache.set(contentHash, content);
                logger.debug(`Added content with hash ${contentHash} to cache, size: ${content.length} bytes`);
            } else {
                logger.debug(`Skipped caching content with hash ${contentHash} due to high memory pressure (${(memoryPressure * 100).toFixed(1)}%)`);
            }

            logger.debug(`Retrieved content with hash ${contentHash}, size: ${content.length} bytes`);
            return content;
        } catch (error: any) {
            logger.error(`Error retrieving content with hash ${contentHash}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Get content as a stream from the content-addressable storage
     * @param contentHash Content hash
     * @returns Promise resolving to a readable stream, or null if not found
     */
    public async getContentStream(contentHash: string): Promise<Readable | null> {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            // Get content metadata
            const metadata = await this.getContentMetadata(contentHash);
            if (!metadata) {
                logger.warn(`Content with hash ${contentHash} not found`);
                return null;
            }

            // Update access statistics
            await this.updateAccessStats(contentHash);

            // Create a readable stream from the file
            const fullPath = path.join(this.basePath, metadata.contentPath);
            const stream = createReadStream(fullPath);

            logger.debug(`Created stream for content with hash ${contentHash}, size: ${metadata.size} bytes`);
            return stream;
        } catch (error: any) {
            logger.error(`Error creating stream for content with hash ${contentHash}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Get content using streaming approach for large files
     * @param contentHash Content hash
     * @param metadata Content metadata
     * @returns Promise resolving to the content buffer
     * @private
     */
    private async getContentStreaming(contentHash: string, metadata: ContentMetadata): Promise<Buffer> {
        // Get a buffer from the pool
        const buffer = Buffer.alloc(metadata.size);
        let position = 0;

        // Create a readable stream from the file
        const fullPath = path.join(this.basePath, metadata.contentPath);
        const stream = createReadStream(fullPath, {
            highWaterMark: 64 * 1024 // 64KB chunks
        });

        // Process the stream
        return new Promise<Buffer>((resolve, reject) => {
            stream.on('data', (chunk: Buffer | string) => {
                // Convert string to Buffer if needed
                const chunkBuffer = typeof chunk === 'string' ? Buffer.from(chunk) : chunk;

                // Copy chunk to buffer
                chunkBuffer.copy(buffer, position);
                position += chunkBuffer.length;
            });

            stream.on('end', () => {
                logger.debug(`Streamed content with hash ${contentHash}, size: ${metadata.size} bytes`);
                resolve(buffer);
            });

            stream.on('error', (error) => {
                logger.error(`Error streaming content with hash ${contentHash}: ${error.message || error}`);
                reject(error);
            });
        });
    }

    /**
     * Update access statistics for content
     * @param contentHash Content hash
     * @private
     */
    private async updateAccessStats(contentHash: string): Promise<void> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                return;
            }

            // Update last access time and access count
            await this.databaseService.run(`
                UPDATE ContentStore
                SET lastAccessTime = ?, accessCount = accessCount + 1
                WHERE contentHash = ?
            `, [Date.now(), contentHash]);
        } catch (error: any) {
            logger.error(`Error updating access stats: ${error.message || error}`);
            // Continue despite error
        }
    }

    /**
     * Check memory pressure and clear cache if needed
     * This helps prevent out-of-memory errors
     */
    private checkMemoryPressure(): void {
        try {
            const memoryUsage = process.memoryUsage();
            const heapUsed = memoryUsage.heapUsed;
            const heapTotal = memoryUsage.heapTotal;
            const memoryPressure = heapUsed / heapTotal;

            // If memory pressure is high, clear part of the cache
            if (memoryPressure > 0.8) {
                const cacheSize = this.contentCache.size;
                const itemsToRemove = Math.ceil(cacheSize * 0.5); // Remove 50% of cache items

                logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%), clearing ${itemsToRemove} items from content cache`);

                // Get cache keys
                const keys = Array.from(this.contentCache.keys()).slice(0, itemsToRemove);

                // Remove items from cache
                for (const key of keys) {
                    this.contentCache.delete(key);
                }

                // Force garbage collection if available
                if (global.gc) {
                    global.gc();
                }
            }
        } catch (error) {
            logger.error('Error checking memory pressure:', error);
        }
    }

    /**
     * Remove content from the content-addressable storage
     * @param contentHash Content hash
     * @returns Promise resolving to true if content was removed, false otherwise
     */
    public async removeContent(contentHash: string): Promise<boolean> {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            // Get content metadata
            const metadata = await this.getContentMetadata(contentHash);
            if (!metadata) {
                logger.warn(`Content with hash ${contentHash} not found`);
                return false;
            }

            // Decrement reference count
            const newRefCount = await this.decrementReferenceCount(contentHash);

            // If reference count is zero, remove the content
            if (newRefCount <= 0) {
                // Remove content file
                const fullPath = path.join(this.basePath, metadata.contentPath);
                await fsPromises.unlink(fullPath);

                // Remove content metadata from database
                await this.removeContentMetadata(contentHash);

                // Remove from cache
                this.contentCache.delete(contentHash);

                logger.debug(`Removed content with hash ${contentHash}`);
                return true;
            }

            logger.debug(`Decremented reference count for content with hash ${contentHash}, new count: ${newRefCount}`);
            return false;
        } catch (error: any) {
            logger.error(`Error removing content with hash ${contentHash}: ${error.message || error}`);
            return false;
        }
    }

    /**
     * Clean up unused content
     * @param olderThan Age in seconds (default: 30 days)
     * @returns Promise resolving to the number of content items removed
     */
    public async cleanupUnusedContent(olderThan: number = 30 * 24 * 60 * 60): Promise<number> {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            logger.info(`Cleaning up unused content older than ${olderThan} seconds`);

            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, skipping unused content cleanup');
                return 0;
            }

            // Get unused content
            const cutoffTime = Date.now() - olderThan * 1000;
            const unusedContent = await this.databaseService.executeQuery(`
                SELECT contentHash, contentPath
                FROM ContentStore
                WHERE referenceCount <= 0 AND timestamp < ?
            `, [cutoffTime]);

            if (!unusedContent || unusedContent.length === 0) {
                logger.info('No unused content to clean up');
                return 0;
            }

            // Remove unused content
            let removedCount = 0;
            for (const content of unusedContent) {
                try {
                    // Remove content file
                    const fullPath = path.join(this.basePath, content.contentPath);
                    await fsPromises.unlink(fullPath);

                    // Remove content metadata from database
                    await this.removeContentMetadata(content.contentHash);

                    // Remove from cache
                    this.contentCache.delete(content.contentHash);

                    removedCount++;
                } catch (error: any) {
                    logger.error(`Error removing unused content with hash ${content.contentHash}: ${error.message || error}`);
                }
            }

            logger.info(`Removed ${removedCount} unused content items`);
            return removedCount;
        } catch (error: any) {
            logger.error(`Error cleaning up unused content: ${error.message || error}`);
            return 0;
        }
    }

    /**
     * Generate a content path from a hash
     * @param contentHash Content hash
     * @returns Content path
     * @private
     */
    private generateContentPath(contentHash: string): string {
        // Create sharded path
        const shards = [];
        for (let i = 0; i < this.shardingLevels; i++) {
            shards.push(contentHash.substring(i * this.shardingWidth, (i + 1) * this.shardingWidth));
        }

        return path.join(...shards, contentHash);
    }

    /**
     * Store content metadata in the database
     * @param metadata Content metadata
     * @private
     */
    private async storeContentMetadata(metadata: ContentMetadata): Promise<void> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, skipping content metadata storage');
                return;
            }

            await this.databaseService.run(`
                INSERT OR REPLACE INTO ContentStore (
                    contentHash, contentPath, size, contentType, referenceCount, timestamp,
                    fingerprint, lastAccessTime, accessCount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                metadata.contentHash,
                metadata.contentPath,
                metadata.size,
                metadata.contentType,
                metadata.referenceCount,
                metadata.timestamp,
                metadata.fingerprint || null,
                metadata.lastAccessTime || null,
                metadata.accessCount || 0
            ]);
        } catch (error: any) {
            logger.error(`Error storing content metadata: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Get content metadata from the database
     * @param contentHash Content hash
     * @returns Promise resolving to the content metadata, or null if not found
     * @private
     */
    private async getContentMetadata(contentHash: string): Promise<ContentMetadata | null> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, cannot get content metadata');
                return null;
            }

            const result = await this.databaseService.executeQuery(`
                SELECT * FROM ContentStore WHERE contentHash = ?
            `, [contentHash]);

            // Return the first result if available
            return result && result.length > 0 ? result[0] as ContentMetadata : null;
        } catch (error: any) {
            logger.error(`Error getting content metadata: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Remove content metadata from the database
     * @param contentHash Content hash
     * @private
     */
    private async removeContentMetadata(contentHash: string): Promise<void> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, skipping content metadata removal');
                return;
            }

            await this.databaseService.run(`
                DELETE FROM ContentStore WHERE contentHash = ?
            `, [contentHash]);
        } catch (error: any) {
            logger.error(`Error removing content metadata: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Increment the reference count for content
     * @param contentHash Content hash
     * @returns Promise resolving to the new reference count
     * @private
     */
    private async incrementReferenceCount(contentHash: string): Promise<number> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, skipping reference count increment');
                return 1; // Return 1 as a default reference count
            }

            // Update the reference count
            await this.databaseService.run(`
                UPDATE ContentStore
                SET referenceCount = referenceCount + 1
                WHERE contentHash = ?
            `, [contentHash]);

            // Get the updated reference count
            const result = await this.databaseService.executeQuery(`
                SELECT referenceCount FROM ContentStore
                WHERE contentHash = ?
            `, [contentHash]);

            return result && result.length > 0 ? result[0].referenceCount : 1;
        } catch (error: any) {
            logger.error(`Error incrementing reference count: ${error.message || error}`);
            return 1; // Return 1 as a default reference count
        }
    }

    /**
     * Decrement the reference count for content
     * @param contentHash Content hash
     * @returns Promise resolving to the new reference count
     * @private
     */
    private async decrementReferenceCount(contentHash: string): Promise<number> {
        try {
            // Check if database service is available
            if (!this.databaseService) {
                logger.warn('Database service not available, skipping reference count decrement');
                return 0; // Return 0 to indicate content can be removed
            }

            // Update the reference count
            await this.databaseService.run(`
                UPDATE ContentStore
                SET referenceCount = MAX(0, referenceCount - 1)
                WHERE contentHash = ?
            `, [contentHash]);

            // Get the updated reference count
            const result = await this.databaseService.executeQuery(`
                SELECT referenceCount FROM ContentStore
                WHERE contentHash = ?
            `, [contentHash]);

            return result && result.length > 0 ? result[0].referenceCount : 0;
        } catch (error: any) {
            logger.error(`Error decrementing reference count: ${error.message || error}`);
            return 0;
        }
    }
}
